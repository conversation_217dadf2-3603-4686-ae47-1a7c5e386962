#!/usr/bin/env python3
import asyncio
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from apps.papers.data.services import ASMSearchService
from apps.papers.data.dtos import SearchRequestDTO

async def test_asm_search():
    query = 'Comparative Genomics Reveals a Remarkable Biosynthetic Potential of the Streptomyces Phylogenetic Lineage Associated with Rugose-Ornamented Spores'
    
    print(f'Testing ASM search for: {query}')
    print('=' * 80)
    
    service = ASMSearchService()
    request = SearchRequestDTO(query=query, max_results=10)
    
    try:
        papers = await service.search(request)
        print(f'Found {len(papers)} papers')
        
        if papers:
            for i, paper in enumerate(papers, 1):
                print(f'\n--- Paper {i} ---')
                print(f'Title: {paper.title}')
                print(f'DOI: {paper.doi}')
                print(f'Journal: {paper.journal}')
                print(f'URL: {paper.url}')
                print(f'Authors: <AUTHORS>
                if paper.abstract:
                    print(f'Abstract: {paper.abstract[:200]}...')
        else:
            print('No papers found!')
            
        # Test with a simpler query
        print('\n' + '=' * 80)
        simple_query = 'Streptomyces'
        print(f'Testing with simpler query: {simple_query}')
        
        simple_request = SearchRequestDTO(query=simple_query, max_results=5)
        simple_papers = await service.search(simple_request)
        print(f'Found {len(simple_papers)} papers with simple query')
        
        for i, paper in enumerate(simple_papers, 1):
            print(f'\n--- Simple Search Paper {i} ---')
            print(f'Title: {paper.title}')
            print(f'DOI: {paper.doi}')
            print(f'Journal: {paper.journal}')
            
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()
    finally:
        await service.session.close()

if __name__ == '__main__':
    asyncio.run(test_asm_search()) 