#!/usr/bin/env python3
"""
Simple test script to verify Crossref ASM search functionality without Django.
This tests whether we can find the target Streptomyces paper using the improved search approach.
"""

import asyncio
import aiohttp
import json


async def test_crossref_search():
    """Test Crossref search for ASM papers with different strategies."""
    
    print("=" * 80)
    print("Testing Crossref Search for ASM Papers")
    print("=" * 80)
    
    async with aiohttp.ClientSession() as session:
        # Test multiple search strategies
        search_strategies = [
            "Comparative Genomics Reveals a Remarkable Biosynthetic Potential of the Streptomyces Phylogenetic Lineage Associated with Rugose-Ornamented Spores",
            "Streptomyces biosynthetic potential rugose ornamented spores",
            "Streptomyces microbiology",
            "Streptomyces mSystems OR mBio OR mSphere"
        ]
        
        target_doi = "10.1128/msystems.00489-21"
        target_found = False
        all_asm_papers = []
        
        for i, query in enumerate(search_strategies, 1):
            print(f"\n🔍 Strategy {i}: {query}")
            
            url = "https://api.crossref.org/works"
            params = {
                'query': query,
                'rows': 20,
                'mailto': '<EMAIL>',
                'sort': 'published',
                'order': 'desc'
            }
            
            try:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        items = data.get('message', {}).get('items', [])
                        
                        print(f"   Total results: {len(items)}")
                        
                        # Filter for ASM papers
                        asm_papers = []
                        for item in items:
                            publisher = item.get('publisher', '').lower()
                            container_title = item.get('container-title', [])
                            journal_name = container_title[0].lower() if container_title else ''
                            title = item.get('title', [''])[0] if item.get('title') else ''
                            doi = item.get('DOI', '')
                            
                            # Check for ASM
                            is_asm = 'american society for microbiology' in publisher
                            
                            # Check for ASM journals
                            asm_journals = [
                                'msystems', 'mbio', 'msphere',
                                'applied and environmental microbiology',
                                'antimicrobial agents and chemotherapy',
                                'journal of bacteriology',
                                'journal of clinical microbiology',
                                'infection and immunity',
                                'journal of virology'
                            ]
                            is_asm_journal = any(asm_journal in journal_name for asm_journal in asm_journals)
                            
                            if is_asm or is_asm_journal:
                                # Clean up HTML from title
                                import re
                                clean_title = re.sub(r'<[^>]+>', '', title)
                                clean_title = re.sub(r'\s+', ' ', clean_title).strip()
                                
                                paper_info = {
                                    'title': clean_title,
                                    'doi': doi,
                                    'publisher': item.get('publisher', ''),
                                    'journal': container_title[0] if container_title else 'Unknown'
                                }
                                asm_papers.append(paper_info)
                                
                                # Check if this is our target paper
                                if doi == target_doi:
                                    target_found = True
                                    print(f"\n🎯 TARGET PAPER FOUND!")
                                    print(f"   Title: {clean_title}")
                                    print(f"   DOI: {doi}")
                                    print(f"   Publisher: {item.get('publisher', '')}")
                                    print(f"   Journal: {container_title[0] if container_title else 'Unknown'}")
                        
                        print(f"   ASM papers found: {len(asm_papers)}")
                        
                        for j, paper in enumerate(asm_papers[:3], 1):  # Show first 3
                            print(f"   {j}. {paper['title'][:60]}...")
                            print(f"      DOI: {paper['doi']}")
                            print(f"      Journal: {paper['journal']}")
                        
                        all_asm_papers.extend(asm_papers)
                        
                    else:
                        print(f"   ❌ Request failed with status {response.status}")
                        
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        # Summary
        print(f"\n" + "=" * 80)
        print("Search Summary:")
        print(f"  • Total ASM papers found across all strategies: {len(all_asm_papers)}")
        print(f"  • Target paper found: {'✅' if target_found else '❌'}")
        
        if not target_found:
            print(f"\n⚠️  Target paper ({target_doi}) was not found.")
            print("Let's try a direct search for this specific DOI...")
            
            # Direct DOI search
            await test_direct_doi_search(session, target_doi)
        
        print("=" * 80)


async def test_direct_doi_search(session, doi):
    """Test direct search for a specific DOI."""
    
    print(f"\n🔍 Direct DOI search for: {doi}")
    
    url = f"https://api.crossref.org/works/{doi}"
    
    try:
        async with session.get(url) as response:
            if response.status == 200:
                data = await response.json()
                item = data.get('message', {})
                
                title = item.get('title', [''])[0] if item.get('title') else ''
                publisher = item.get('publisher', '')
                container_title = item.get('container-title', [])
                journal = container_title[0] if container_title else 'Unknown'
                
                print(f"✅ DOI found in Crossref!")
                print(f"   Title: {title}")
                print(f"   Publisher: {publisher}")
                print(f"   Journal: {journal}")
                
                # Check if it's from ASM
                if 'american society for microbiology' in publisher.lower():
                    print(f"✅ Confirmed: This is an ASM paper!")
                else:
                    print(f"⚠️  Publisher is not ASM: {publisher}")
                    
            else:
                print(f"❌ DOI not found in Crossref (status: {response.status})")
                
    except Exception as e:
        print(f"❌ Error searching for DOI: {e}")


async def main():
    """Main test function."""
    await test_crossref_search()


if __name__ == "__main__":
    asyncio.run(main()) 