# 🧾 Paper Downloader

A full-stack application for searching and downloading research papers from multiple academic sources, built with Django (backend) and Next.js (frontend) following strict MVVM architecture.

## 🚀 Quick Start (Single Command)

### Option 1: NPM Script (Recommended)
```bash
npm run dev
```

### Option 2: Shell Script
```bash
./start.sh
```

### Option 3: Direct Command
```bash
npm run start
```

**That's it!** Both backend and frontend will start together with unified logging in a single terminal.

## 📊 Access Points

| Service | URL | Description |
|---------|-----|-------------|
| **Frontend** | http://localhost:3000 | Main user interface |
| **Backend API** | http://localhost:8000/api | REST API endpoints |
| **Django Admin** | http://localhost:8000/admin | Admin interface |
| **API Documentation** | http://localhost:8000/api/v1/docs | Interactive API docs |

## 🎯 Features

- **Unified Logging**: All services log to the same terminal with color-coded prefixes
- **Auto-Recovery**: Automatically handles missing dependencies and database setup
- **Kill Switch**: `Ctrl+C` stops all services cleanly
- **Port Management**: Built-in port conflict resolution
- **Development Tools**: Comprehensive development and testing scripts

## 📋 Available Scripts

```bash
# Development
npm run dev          # Start both backend and frontend (main command)
npm run start        # Alias for dev
./start.sh          # Shell script alternative

# Setup & Installation  
npm run setup       # Install all dependencies and setup database
npm run install:all # Install frontend dependencies only
npm run clean       # Clean and reinstall frontend dependencies

# Database Management
npm run reset:db    # Reset SQLite database and run migrations

# Individual Services
npm run dev:backend   # Start only Django backend
npm run dev:frontend  # Start only Next.js frontend

# Testing & Building
npm run test        # Run tests for both services
npm run build       # Build frontend for production

# Utilities
npm run kill:ports  # Kill processes on ports 3000 and 8000
```

## 🛠️ Development Setup

### Prerequisites
- **Node.js** 18+ 
- **Python** 3.13+
- **Git**

### First Time Setup
```bash
# Clone repository
git clone <your-repo-url>
cd paper-downloader

# Automated setup (recommended)
npm run setup

# Manual setup (if needed)
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
python manage.py migrate
cd ../frontend
npm install
cd ..
```

## 🏗️ Architecture (MVVM)

```
paper-downloader/
├── backend/                 # Django Backend
│   ├── apps/papers/        # Main app following MVVM
│   │   ├── data/           # Models (Data Layer)
│   │   ├── domain/         # Business Logic
│   │   └── presentation/   # Views & APIs
│   ├── venv/               # Python virtual environment
│   └── manage.py          # Django management
├── frontend/               # Next.js Frontend  
│   ├── app/                # Next.js app router
│   ├── components/         # MVVM Views
│   │   ├── features/       # Feature-specific components
│   │   ├── layout/         # Layout components
│   │   └── ui/             # Reusable UI components
│   └── lib/                # MVVM ViewModels & Services
└── package.json           # Root package configuration
```

## 🧪 Testing the Application

1. **Start the application**: `npm run dev`
2. **Open browser**: http://localhost:3000
3. **Search papers**: Enter "machine learning" and click search
4. **Download papers**: Select papers and click download
5. **Check downloads**: Files saved in `/backend/downloads/`

## 📚 Paper Sources

| Source | Status | Description |
|--------|--------|-------------|
| **arXiv** | ✅ Active | Open access preprints |
| **Crossref** | ✅ Active | Academic publications metadata |
| **PubMed** | ✅ Active | Biomedical literature |
| **Google Scholar** | ⚠️ Limited | Requires special setup |
| **IEEE Xplore** | ⚠️ Limited | Requires special setup |

## 🔧 Troubleshooting

### Common Issues

#### **Port Already in Use**
```bash
npm run kill:ports  # Kill processes on 3000 and 8000
```

#### **Missing Dependencies**
```bash
npm run clean       # Clean and reinstall frontend
npm run setup       # Full setup including backend
```

#### **Database Issues**
```bash
npm run reset:db    # Reset database completely
```

#### **Virtual Environment Issues**
```bash
cd backend
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### Log Analysis

The unified logging shows:
- **[backend]** in blue - Django server logs
- **[frontend]** in green - Next.js development server logs

## 🔄 Development Workflow

1. **Start development**: `npm run dev`
2. **Make changes**: Edit files in `/backend/` or `/frontend/`
3. **Auto-reload**: Both servers automatically reload on changes
4. **Test changes**: Use the web interface or API endpoints
5. **Run tests**: `npm run test`
6. **Build for production**: `npm run build`

## 📝 Environment Variables

Create `.env` files as needed:

**Backend** (`backend/.env`):
```env
DEBUG=True
SECRET_KEY=your-secret-key
DATABASE_URL=sqlite:///db.sqlite3
```

**Frontend** (`frontend/.env.local`):
```env
NEXT_PUBLIC_API_URL=http://localhost:8000/api
```

## 🤝 Contributing

1. Follow MVVM architecture strictly
2. Use the unified development setup
3. Test with `npm run test` before committing
4. Update documentation as needed

## 📄 License

MIT License - see LICENSE file for details 