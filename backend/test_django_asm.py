#!/usr/bin/env python3
import os
import asyncio
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from apps.papers.data.services import ASMSearchService
from apps.papers.data.models import SearchRequestDTO

async def test_asm_search():
    """Test ASM search functionality."""
    print("🧪 Testing ASM Search Service...")
    
    service = ASMSearchService()
    async with service:
        # Test 1: Target paper search
        print("\n🔍 Test 1: Searching for target Streptomyces paper...")
        request1 = SearchRequestDTO(
            query='Comparative Genomics Reveals a Remarkable Biosynthetic Potential of the Streptomyces Phylogenetic Lineage Associated with Rugose-Ornamented Spores',
            max_results=5
        )
        papers1 = await service.search(request1)
        print(f'Found: {len(papers1)} papers')
        
        target_found = False
        for paper in papers1:
            print(f'📄 {paper.title[:60]}...')
            print(f'   DOI: {paper.doi}')
            print(f'   Journal: {paper.journal}')
            if paper.doi == "10.1128/msystems.00489-21":
                target_found = True
                print("   🎯 TARGET PAPER FOUND!")
            print()
        
        # Test 2: Simple keywords
        print("🔍 Test 2: Simple keywords search...")
        request2 = SearchRequestDTO(query='Streptomyces biosynthetic', max_results=5)
        papers2 = await service.search(request2)
        print(f'Found: {len(papers2)} papers for simple keywords')
        
        for paper in papers2[:3]:  # Show first 3
            print(f'📄 {paper.title[:60]}...')
            print(f'   DOI: {paper.doi}')
            print(f'   Journal: {paper.journal}')
            print()
        
        print(f"\n✅ Test Summary:")
        print(f"   Target paper found: {'✅' if target_found else '❌'}")
        print(f"   Total papers from test 1: {len(papers1)}")
        print(f"   Total papers from test 2: {len(papers2)}")

if __name__ == "__main__":
    asyncio.run(test_asm_search()) 