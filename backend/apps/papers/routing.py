"""
WebSocket routing for real-time notifications.
"""

from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    re_path(r'ws/downloads/$', consumers.DownloadProgressConsumer.as_asgi()),
    re_path(r'ws/papers/search/(?P<query_id>\w+)/$', consumers.SearchProgressConsumer.as_asgi()),
    re_path(r'ws/papers/download/(?P<task_id>\w+)/$', consumers.DownloadProgressConsumer.as_asgi()),
] 