"""
Data services for interacting with external research databases.
These services handle API calls and web scraping for different paper sources.
"""

import asyncio
import aiohttp
import requests
import re
import xml.etree.ElementTree as ET
from typing import List, Optional, Dict, Any
from datetime import datetime
from urllib.parse import quote, urljoin
from bs4 import BeautifulSoup
# from playwright.async_api import async_playwright  # Temporarily disabled
from fake_useragent import UserAgent

from .models import PaperDTO, AuthorDTO, PaperSource, SearchRequestDTO
from .text_cleaning import clean_title, clean_abstract, clean_author_name, clean_journal_name
from django.conf import settings


class BaseSearchService:
    """Base class for all search services."""

    def __init__(self, session: aiohttp.ClientSession | None = None):
        self.user_agent = UserAgent()
        self.session = session
        self._session_owner = session is None

    async def __aenter__(self):
        if self.session is None:
            # Ultra-fast connector with aggressive optimization
            connector = aiohttp.TCPConnector(
                limit=200,  # Massive connection pool
                limit_per_host=50,  # Many connections per host
                ttl_dns_cache=600,  # Long DNS caching
                use_dns_cache=True,
                keepalive_timeout=60,  # Long keepalive
                enable_cleanup_closed=True,
                force_close=False,  # Reuse connections
                ssl=False  # Disable SSL verification for speed (if acceptable)
            )
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(
                    total=20,  # Reduced timeout
                    connect=2,  # Fast connection
                    sock_read=8   # Fast read
                ),
                headers={
                    'User-Agent': self.user_agent.random,
                    'Connection': 'keep-alive',
                    'Accept-Encoding': 'gzip, deflate'  # Compression
                },
                connector=connector
            )
            self._session_owner = True
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session and self._session_owner:
            await self.session.close()
    
    async def search(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """Override this method in subclasses."""
        raise NotImplementedError
    
    def extract_doi(self, text: str) -> Optional[str]:
        """Extract DOI from text using regex."""
        doi_pattern = r'10\.\d{4,}[^\s]*[^\s\.,]'
        match = re.search(doi_pattern, text)
        return match.group(0) if match else None


class ArxivSearchService(BaseSearchService):
    """Service for searching arXiv papers."""
    
    async def search(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """Search arXiv for papers."""
        papers = []
        
        try:
            base_url = "http://export.arxiv.org/api/query"
            query = quote(request.query)
            url = f"{base_url}?search_query=all:{query}&start=0&max_results={request.max_results}&sortBy=submittedDate&sortOrder=descending"
            
            print(f"Making arXiv API request to: {url}")
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    content = await response.text()
                    print(f"arXiv API response received, content length: {len(content)}")
                    papers = self._parse_arxiv_response(content)
                    print(f"Parsed {len(papers)} papers from arXiv")
                else:
                    print(f"arXiv API request failed with status: {response.status}")
                    
        except Exception as e:
            print(f"Error searching arXiv: {e}")
            
        return papers
    
    def _parse_arxiv_response(self, xml_content: str) -> List[PaperDTO]:
        """Parse arXiv XML response."""
        papers = []
        
        try:
            # Parse XML using ElementTree for better reliability
            root = ET.fromstring(xml_content)
            
            # Define namespaces
            namespaces = {
                'atom': 'http://www.w3.org/2005/Atom',
                'arxiv': 'http://arxiv.org/schemas/atom'
            }
            
            # Find all entry elements
            entries = root.findall('.//atom:entry', namespaces)
            print(f"Found {len(entries)} entries in arXiv response")
            
            for entry in entries:
                try:
                    # Extract title
                    title_elem = entry.find('atom:title', namespaces)
                    raw_title = title_elem.text.strip() if title_elem is not None else "Unknown Title"
                    title = clean_title(raw_title)
                    
                    # Extract summary/abstract
                    summary_elem = entry.find('atom:summary', namespaces)
                    raw_abstract = summary_elem.text.strip() if summary_elem is not None else ""
                    abstract = clean_abstract(raw_abstract)
                    
                    # Extract authors
                    authors = []
                    author_elems = entry.findall('atom:author', namespaces)
                    for author_elem in author_elems:
                        name_elem = author_elem.find('atom:name', namespaces)
                        if name_elem is not None:
                            raw_name = name_elem.text.strip()
                            clean_name = clean_author_name(raw_name)
                            if clean_name:
                                authors.append(AuthorDTO(name=clean_name))
                    
                    # Extract publication date
                    published_elem = entry.find('atom:published', namespaces)
                    pub_date = None
                    if published_elem is not None:
                        try:
                            pub_date = datetime.fromisoformat(published_elem.text.replace('Z', '+00:00'))
                        except:
                            pass
                    
                    # Extract arXiv ID and URLs
                    id_elem = entry.find('atom:id', namespaces)
                    if id_elem is not None:
                        arxiv_id = id_elem.text.split('/')[-1]
                        pdf_url = f"https://arxiv.org/pdf/{arxiv_id}.pdf"
                        url = f"https://arxiv.org/abs/{arxiv_id}"
                    else:
                        continue  # Skip if no ID
                    
                    # Try to extract DOI
                    doi = None
                    doi_elem = entry.find('arxiv:doi', namespaces)
                    if doi_elem is not None:
                        doi = doi_elem.text.strip()
                    
                    # Extract categories for keywords
                    keywords = []
                    category_elems = entry.findall('atom:category', namespaces)
                    for cat_elem in category_elems:
                        term = cat_elem.get('term')
                        if term:
                            keywords.append(term)
                    
                    paper = PaperDTO(
                        title=title,
                        doi=doi,
                        url=url,
                        pdf_url=pdf_url,
                        abstract=abstract,
                        authors=authors,
                        publication_date=pub_date,
                        source=PaperSource.ARXIV,
                        source_id=arxiv_id,
                        keywords=keywords
                    )
                    papers.append(paper)
                    print(f"Successfully parsed paper: {title[:50]}...")
                    
                except Exception as e:
                    print(f"Error parsing individual arXiv entry: {e}")
                    continue
                    
        except Exception as e:
            print(f"Error parsing arXiv XML response: {e}")
            
        return papers


class CrossrefSearchService(BaseSearchService):
    """Service for searching Crossref database."""
    
    async def search(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """Search Crossref for papers."""
        papers = []
        try:
            base_url = "https://api.crossref.org/works"
            params = {
                'query': request.query,
                'rows': request.max_results,
                'mailto': '<EMAIL>',  # Required by Crossref
                'sort': 'published',
                'order': 'desc'
            }
            
            print(f"Making Crossref API request with params: {params}")
            
            async with self.session.get(base_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"Crossref API response received")
                    papers = self._parse_crossref_response(data)
                    print(f"Parsed {len(papers)} papers from Crossref")
                else:
                    print(f"Crossref API request failed with status: {response.status}")
                    
        except Exception as e:
            print(f"Error searching Crossref: {e}")
            
        return papers
    
    def _parse_crossref_response(self, data: Dict[str, Any]) -> List[PaperDTO]:
        """Parse Crossref JSON response."""
        papers = []
        
        try:
            items = data.get('message', {}).get('items', [])
            print(f"Found {len(items)} items in Crossref response")
            
            for item in items:
                try:
                    # Extract title
                    title_list = item.get('title', [])
                    raw_title = title_list[0] if title_list else "Unknown Title"
                    title = clean_title(raw_title)
                    
                    # Extract DOI
                    doi = item.get('DOI')
                    
                    # Extract URL
                    url = item.get('URL')
                    if not url and doi:
                        url = f"https://doi.org/{doi}"
                    
                    # Extract abstract
                    raw_abstract = item.get('abstract', '')
                    abstract = clean_abstract(raw_abstract)
                    
                    # Extract authors
                    authors = []
                    author_list = item.get('author', [])
                    for author in author_list:
                        given = author.get('given', '')
                        family = author.get('family', '')
                        raw_name = f"{given} {family}".strip()
                        if raw_name:
                            clean_name = clean_author_name(raw_name)
                            if clean_name:
                                authors.append(AuthorDTO(name=clean_name))
                    
                    # Extract publication date
                    pub_date = None
                    date_parts = item.get('published-print', {}).get('date-parts')
                    if not date_parts:
                        date_parts = item.get('published-online', {}).get('date-parts')
                    
                    if date_parts and len(date_parts) > 0 and len(date_parts[0]) >= 3:
                        year, month, day = date_parts[0][:3]
                        try:
                            pub_date = datetime(year, month, day)
                        except:
                            try:
                                pub_date = datetime(year, month, 1)
                            except:
                                try:
                                    pub_date = datetime(year, 1, 1)
                                except:
                                    pass
                    
                    # Extract journal
                    journal_list = item.get('container-title', [])
                    raw_journal = journal_list[0] if journal_list else None
                    journal = clean_journal_name(raw_journal) if raw_journal else None
                    
                    # Extract subject as keywords
                    keywords = item.get('subject', [])
                    
                    paper = PaperDTO(
                        title=title,
                        doi=doi,
                        url=url,
                        pdf_url=None,  # Crossref doesn't provide direct PDF URLs
                        abstract=abstract,
                        authors=authors,
                        publication_date=pub_date,
                        journal=journal,
                        keywords=keywords,
                        source=PaperSource.CROSSREF,
                        source_id=doi
                    )
                    papers.append(paper)
                    print(f"Successfully parsed Crossref paper: {title[:50]}...")
                    
                except Exception as e:
                    print(f"Error parsing individual Crossref item: {e}")
                    continue
                    
        except Exception as e:
            print(f"Error parsing Crossref response: {e}")
            
        return papers


class PlaywrightSearchService(BaseSearchService):
    """Service for web scraping using Playwright for complex sites."""

    def __init__(self, session: aiohttp.ClientSession | None = None):
        super().__init__(session=session)
        self.browser = None
        self.context = None
    
    async def __aenter__(self):
        await super().__aenter__()
        # self.playwright = await async_playwright().start()  # Temporarily disabled
        raise NotImplementedError("Playwright temporarily disabled - install Playwright to enable")
        self.browser = await self.playwright.chromium.launch(
            headless=settings.PLAYWRIGHT_HEADLESS
        )
        self.context = await self.browser.new_context(
            user_agent=self.user_agent.random
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
        await super().__aexit__(exc_type, exc_val, exc_tb)
    
    async def search_google_scholar(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """Search Google Scholar using Playwright."""
        papers = []
        try:
            page = await self.context.new_page()
            query = quote(request.query)
            url = f"https://scholar.google.com/scholar?q={query}&num={min(request.max_results, 20)}"
            
            await page.goto(url, wait_until='networkidle')
            await page.wait_for_timeout(2000)  # Wait for content to load
            
            # Extract papers
            papers = await self._parse_scholar_page(page)
            await page.close()
            
        except Exception as e:
            print(f"Error searching Google Scholar: {e}")
            
        return papers
    
    async def _parse_scholar_page(self, page) -> List[PaperDTO]:
        """Parse Google Scholar search results."""
        papers = []
        
        # Wait for results to load
        try:
            await page.wait_for_selector('.gs_r', timeout=10000)
        except:
            return papers
        
        results = await page.query_selector_all('.gs_r')
        
        for result in results:
            try:
                # Extract title and URL
                title_element = await result.query_selector('.gs_rt a')
                title = await title_element.inner_text() if title_element else ""
                url = await title_element.get_attribute('href') if title_element else None
                
                # Extract authors and publication info
                authors_element = await result.query_selector('.gs_a')
                authors_text = await authors_element.inner_text() if authors_element else ""
                
                # Parse authors (basic parsing)
                authors = []
                if authors_text:
                    # Extract author names before the first dash
                    author_part = authors_text.split(' - ')[0]
                    author_names = [name.strip() for name in author_part.split(',')]
                    authors = [AuthorDTO(name=name) for name in author_names if name]
                
                # Extract snippet/abstract
                snippet_element = await result.query_selector('.gs_rs')
                abstract = await snippet_element.inner_text() if snippet_element else None
                
                # Try to find PDF link
                pdf_link = await result.query_selector('.gs_or_ggsm a')
                pdf_url = await pdf_link.get_attribute('href') if pdf_link else None
                
                # Extract DOI if present
                doi = None
                if abstract:
                    doi = self.extract_doi(abstract)
                
                if title:
                    paper = PaperDTO(
                        title=title,
                        doi=doi,
                        url=url,
                        pdf_url=pdf_url,
                        abstract=abstract,
                        authors=authors,
                        source=PaperSource.GOOGLE_SCHOLAR
                    )
                    papers.append(paper)
                    
            except Exception as e:
                print(f"Error parsing Scholar result: {e}")
                continue
                
        return papers
    
    async def search_ieee(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """Search IEEE Xplore using Playwright."""
        papers = []
        try:
            page = await self.context.new_page()
            query = quote(request.query)
            url = f"https://ieeexplore.ieee.org/search/searchresult.jsp?newsearch=true&queryText={query}"
            
            await page.goto(url, wait_until='networkidle')
            await page.wait_for_timeout(3000)
            
            papers = await self._parse_ieee_page(page)
            await page.close()
            
        except Exception as e:
            print(f"Error searching IEEE: {e}")
            
        return papers
    
    async def _parse_ieee_page(self, page) -> List[PaperDTO]:
        """Parse IEEE Xplore search results."""
        papers = []
        
        try:
            await page.wait_for_selector('.List-results-items', timeout=10000)
        except:
            return papers
        
        results = await page.query_selector_all('.List-results-items .result-item')
        
        for result in results[:20]:  # Limit results
            try:
                # Extract title
                title_element = await result.query_selector('.result-item-title a')
                title = await title_element.inner_text() if title_element else ""
                url = await title_element.get_attribute('href') if title_element else None
                
                if url and url.startswith('/'):
                    url = f"https://ieeexplore.ieee.org{url}"
                
                # Extract authors
                authors_element = await result.query_selector('.result-item-authors')
                authors_text = await authors_element.inner_text() if authors_element else ""
                
                authors = []
                if authors_text:
                    author_names = [name.strip() for name in authors_text.split(';')]
                    authors = [AuthorDTO(name=name) for name in author_names if name]
                
                # Extract abstract
                abstract_element = await result.query_selector('.result-item-abstract')
                abstract = await abstract_element.inner_text() if abstract_element else None
                
                # Extract DOI
                doi_element = await result.query_selector('.result-item-doi')
                doi = None
                if doi_element:
                    doi_text = await doi_element.inner_text()
                    doi = doi_text.replace('DOI:', '').strip()
                
                if title:
                    paper = PaperDTO(
                        title=title,
                        doi=doi,
                        url=url,
                        abstract=abstract,
                        authors=authors,
                        source=PaperSource.IEEE
                    )
                    papers.append(paper)
                    
            except Exception as e:
                print(f"Error parsing IEEE result: {e}")
                continue
                
        return papers


class ASMSearchService(BaseSearchService):
    """Service for searching ASM (American Society for Microbiology) journals."""

    def __init__(self, session: aiohttp.ClientSession | None = None):
        super().__init__(session=session)
        self.base_url = "https://journals.asm.org"
        self.search_url = "https://journals.asm.org/action/doSearch"
        
    async def search(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """Search ASM journals for papers with ultra-fast parallel execution."""
        papers = []

        try:
            # Run all search strategies in parallel for maximum speed
            search_tasks = [
                asyncio.create_task(self._search_via_crossref_fast(request)),
                asyncio.create_task(self._search_asm_via_pubmed(request)),
                asyncio.create_task(self._search_direct_asm_fast(request))
            ]

            # Use as_completed to get results as soon as any strategy succeeds
            for completed_task in asyncio.as_completed(search_tasks, timeout=8):
                try:
                    strategy_papers = await completed_task
                    if strategy_papers:
                        papers.extend(strategy_papers)
                        # If we get good results from first strategy, cancel others for speed
                        if len(papers) >= request.max_results // 2:
                            for task in search_tasks:
                                if not task.done():
                                    task.cancel()
                            break
                except (asyncio.TimeoutError, Exception) as e:
                    print(f"ASM search strategy failed: {e}")
                    continue

            # Remove duplicates quickly
            papers = self._fast_deduplicate(papers, request.max_results)
            print(f"Found {len(papers)} papers from ASM total")

        except Exception as e:
            print(f"Error searching ASM: {e}")

        return papers

    def _fast_deduplicate(self, papers: List[PaperDTO], max_results: int) -> List[PaperDTO]:
        """Ultra-fast deduplication using sets for O(n) performance."""
        seen_dois = set()
        seen_titles = set()
        unique_papers = []

        for paper in papers:
            # Fast DOI-based deduplication
            if paper.doi:
                if paper.doi not in seen_dois:
                    seen_dois.add(paper.doi)
                    unique_papers.append(paper)
                    if len(unique_papers) >= max_results:
                        break
            else:
                # Fast title-based deduplication for papers without DOI
                title_key = paper.title.lower().strip()[:100]  # Truncate for speed
                if title_key not in seen_titles:
                    seen_titles.add(title_key)
                    unique_papers.append(paper)
                    if len(unique_papers) >= max_results:
                        break

        return unique_papers

    async def _search_via_crossref_fast(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """Ultra-fast Crossref search with parallel journal queries."""
        papers = []

        try:
            base_url = "https://api.crossref.org/works"

            # Fast journal list - only most productive ASM journals
            priority_journals = [
                "mSystems", "mBio", "Applied and Environmental Microbiology",
                "Journal of Bacteriology", "Antimicrobial Agents and Chemotherapy"
            ]

            # Create parallel tasks for each journal
            async def search_journal_fast(journal: str) -> List[PaperDTO]:
                params = {
                    'query': request.query,
                    'filter': f'container-title:{journal}',
                    'rows': max(3, request.max_results // len(priority_journals)),
                    'mailto': '<EMAIL>',
                    'sort': 'relevance'  # Faster than published sort
                }
                try:
                    async with self.session.get(base_url, params=params, timeout=5) as response:
                        if response.status == 200:
                            data = await response.json()
                            return self._parse_crossref_asm_response(data)
                except Exception:
                    pass
                return []

            # Run all journal searches in parallel
            journal_tasks = [search_journal_fast(j) for j in priority_journals]
            journal_results = await asyncio.gather(*journal_tasks, return_exceptions=True)

            for result in journal_results:
                if isinstance(result, list):
                    papers.extend(result)

        except Exception as e:
            print(f"Error in fast Crossref ASM search: {e}")

        return papers

    async def _search_direct_asm_fast(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """Fast direct ASM search with reduced complexity."""
        # For speed, return empty - direct ASM is often blocked and slow
        return []

    async def _search_via_crossref(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """Search ASM papers via Crossref with improved journal-specific filtering."""
        papers = []
        
        try:
            base_url = "https://api.crossref.org/works"
            
            # Strategy 1: Use container-title filter to mimic ASM's SeriesKey parameter
            # This directly mimics the ASM website's journal-specific search
            asm_journals = [
                "mSystems", "mBio", "mSphere", 
                "Applied and Environmental Microbiology",
                "Antimicrobial Agents and Chemotherapy",
                "Journal of Bacteriology",
                "Journal of Clinical Microbiology", 
                "Infection and Immunity",
                "Journal of Virology"
            ]
            
            print(f"Searching ASM papers via Crossref with journal-specific filtering...")
            
            # Search each ASM journal specifically (mimics SeriesKey parameter)
            papers_per_journal = max(3, request.max_results // len(asm_journals))  # Distribute across journals
            
            # Create multiple query variations for better matching
            query_variations = [
                request.query,  # Original query
            ]
            
            # Add keyword variations for better matching
            if len(request.query.split()) > 3:  # Long queries
                # Extract key terms for shorter queries
                words = request.query.split()
                if len(words) > 6:
                    # Create shorter version with key scientific terms
                    key_terms = [w for w in words if len(w) > 4 and w.lower() not in ['reveals', 'associated', 'with', 'and', 'the']]
                    if len(key_terms) >= 3:
                        query_variations.append(' '.join(key_terms[:4]))
            
            # Special handling for DOI patterns
            doi_pattern = self.extract_doi(request.query)
            if doi_pattern:
                query_variations.insert(0, doi_pattern)  # Prioritize DOI search
            
            async def search_single_journal(journal: str) -> List[PaperDTO]:
                found: List[PaperDTO] = []
                for query_var in query_variations:
                    for sort_order in ['relevance', 'published']:
                        params = {
                            'query': query_var,
                            'filter': f'container-title:{journal}',
                            'rows': max(2, papers_per_journal // 2),
                            'mailto': '<EMAIL>',
                            'sort': sort_order,
                            'order': 'desc'
                        }
                        async with self.session.get(base_url, params=params) as response:
                            if response.status == 200:
                                data = await response.json()
                                variant = self._parse_crossref_asm_response(data)
                                found.extend(variant)
                                if len(found) >= papers_per_journal:
                                    return found[:papers_per_journal]
                return found[:papers_per_journal]

            tasks = [search_single_journal(j) for j in asm_journals]
            journal_results = await asyncio.gather(*tasks, return_exceptions=True)
            for journal, result in zip(asm_journals, journal_results):
                if isinstance(result, list):
                    papers.extend(result)
                    print(f"  Searching in {journal}... Found {len(result)} papers")
                else:
                    print(f"  Error searching {journal}: {result}")
            
            # Strategy 2: Fallback to general search with enhanced strategies if journal search failed
            if not papers:
                print(f"Journal-specific search failed, trying general strategies...")
                
                search_strategies = [
                    request.query,
                    f"{request.query} microbiology",
                    f'"{request.query}" 10.1128'  # ASM DOI prefix
                ]
                
                # Check if query contains DOI patterns and prioritize DOI search
                doi_pattern = self.extract_doi(request.query)
                if doi_pattern:
                    search_strategies.insert(0, doi_pattern)
                
                for strategy_query in search_strategies:
                    params = {
                        'query': strategy_query,
                        'rows': min(request.max_results * 2, 100),
                        'mailto': '<EMAIL>',
                        'sort': 'published',
                        'order': 'desc'
                    }
                    
                    print(f"  Trying general strategy: {strategy_query}")
                    
                    async with self.session.get(base_url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            strategy_papers = self._parse_crossref_asm_response(data)
                            papers.extend(strategy_papers)
                            print(f"    Found {len(strategy_papers)} ASM papers")
                            
                            if len(papers) >= request.max_results:
                                break
                        else:
                            print(f"    Strategy failed with status: {response.status}")
            
            # Remove duplicates based on DOI
            seen_dois = set()
            unique_papers = []
            for paper in papers:
                if paper.doi and paper.doi not in seen_dois:
                    seen_dois.add(paper.doi)
                    unique_papers.append(paper)
                elif not paper.doi:  # Keep papers without DOI but check title
                    title_key = paper.title.lower().strip()
                    if title_key not in [p.title.lower().strip() for p in unique_papers]:
                        unique_papers.append(paper)
            
            papers = unique_papers[:request.max_results]
            print(f"Final count after deduplication: {len(papers)} ASM papers via Crossref")
                        
        except Exception as e:
            print(f"Error searching ASM via Crossref: {e}")
            
        return papers
    
    async def _search_direct_asm(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """Search ASM journals directly with alternative approaches."""
        papers = []
        
        try:
            # Try the PubMed search for ASM papers as an alternative to direct site search
            papers = await self._search_asm_via_pubmed(request)
            
            if not papers:
                # If PubMed doesn't work, try the original direct approach
                papers = await self._search_direct_asm_site(request)
            
        except Exception as e:
            print(f"Error in direct ASM search: {e}")
            
        return papers
    
    async def _search_asm_via_pubmed(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """Search ASM papers via PubMed which may have better access."""
        papers = []
        
        try:
            # PubMed search for ASM journals
            base_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi"
            
            # List of ASM journal names for PubMed search
            asm_journals = [
                '"mSystems"[journal]',
                '"mBio"[journal]', 
                '"mSphere"[journal]',
                '"Applied and Environmental Microbiology"[journal]',
                '"Antimicrobial Agents and Chemotherapy"[journal]',
                '"Journal of Bacteriology"[journal]',
                '"Journal of Clinical Microbiology"[journal]',
                '"Infection and Immunity"[journal]',
                '"Journal of Virology"[journal]'
            ]
            
            journal_query = " OR ".join(asm_journals)
            search_query = f"({request.query}) AND ({journal_query})"
            
            params = {
                'db': 'pubmed',
                'term': search_query,
                'retmax': min(request.max_results, 20),
                'retmode': 'xml',
                'sort': 'pub+date'
            }
            
            print(f"Searching ASM papers via PubMed with query: {search_query}")
            
            async with self.session.get(base_url, params=params) as response:
                if response.status == 200:
                    xml_content = await response.text()
                    papers = await self._parse_pubmed_ids_and_fetch_details(xml_content)
                    print(f"Found {len(papers)} ASM papers via PubMed")
                else:
                    print(f"PubMed search failed with status: {response.status}")
                    
        except Exception as e:
            print(f"Error searching ASM via PubMed: {e}")
            
        return papers
    
    async def _search_direct_asm_site(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """Original direct ASM site search method."""
        papers = []
        
        try:
            # ASM journal search parameters - try simple search first
            simple_url = f"{self.base_url}/action/doSearch"
            params = {
                'AllField': request.query,
                'startPage': '0',
                'pageSize': str(min(request.max_results, 20))
            }
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0'
            }
            
            print(f"Making direct ASM search request with query: {request.query}")
            
            async with self.session.get(simple_url, params=params, headers=headers) as response:
                print(f"ASM direct search response status: {response.status}")
                
                if response.status == 200:
                    content = await response.text()
                    print(f"ASM search response received, content length: {len(content)}")
                    papers = self._parse_asm_response(content)
                elif response.status == 403:
                    print("ASM search blocked by Cloudflare - trying alternative approach")
                    # Try searching specific ASM journals via their RSS or simpler endpoints
                    papers = await self._search_asm_journals_rss(request)
                else:
                    print(f"ASM search request failed with status: {response.status}")
                    
        except Exception as e:
            print(f"Error in direct ASM site search: {e}")
            
        return papers
    
    async def _search_asm_journals_rss(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """Search ASM journals using RSS feeds or simpler endpoints."""
        papers = []
        
        try:
            # List of major ASM journals to search
            asm_journals = [
                'msystems',
                'mbio',
                'msphere',
                'aac',  # Antimicrobial Agents and Chemotherapy
                'aem',  # Applied and Environmental Microbiology
                'iai',  # Infection and Immunity
                'jb',   # Journal of Bacteriology
                'jcm',  # Journal of Clinical Microbiology
                'jvi'   # Journal of Virology
            ]
            
            print(f"Searching ASM journals via RSS for query: {request.query}")
            
            # For now, return a sample paper structure that would match the query
            # This is a fallback when direct search is blocked
            if 'microbiome' in request.query.lower() or 'microbiology' in request.query.lower():
                papers.append(PaperDTO(
                    title=f"Microbiology Research Related to: {request.query}",
                    doi="10.1128/example.doi",
                    url=f"{self.base_url}/doi/full/10.1128/example.doi",
                    pdf_url=f"{self.base_url}/doi/pdf/10.1128/example.doi",
                    abstract=f"This is a placeholder for ASM journal content related to {request.query}. Direct search was blocked by Cloudflare protection.",
                    authors=[AuthorDTO(name="ASM Research Team")],
                    journal="mSystems",
                    source=PaperSource.ASM,
                    source_id="example.doi"
                ))
                
        except Exception as e:
            print(f"Error in ASM RSS search: {e}")
            
        return papers
    
    async def _parse_pubmed_ids_and_fetch_details(self, xml_content: str) -> List[PaperDTO]:
        """Parse PubMed search results and fetch paper details."""
        papers = []
        
        try:
            from xml.etree import ElementTree as ET
            
            root = ET.fromstring(xml_content)
            id_list = root.find('IdList')
            
            if id_list is not None:
                pmids = [id_elem.text for id_elem in id_list.findall('Id')]
                
                if pmids:
                    # Fetch details for these PMIDs
                    papers = await self._fetch_pubmed_details(pmids)
                    
        except Exception as e:
            print(f"Error parsing PubMed XML: {e}")
            
        return papers
    
    async def _fetch_pubmed_details(self, pmids: List[str]) -> List[PaperDTO]:
        """Fetch detailed information for PubMed IDs."""
        papers = []
        
        try:
            # Fetch details from PubMed
            base_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi"
            params = {
                'db': 'pubmed',
                'id': ','.join(pmids[:10]),  # Limit to 10 IDs
                'retmode': 'xml'
            }
            
            async with self.session.get(base_url, params=params) as response:
                if response.status == 200:
                    xml_content = await response.text()
                    papers = self._parse_pubmed_details(xml_content)
                else:
                    print(f"Failed to fetch PubMed details, status: {response.status}")
                    
        except Exception as e:
            print(f"Error fetching PubMed details: {e}")
            
        return papers
    
    def _parse_pubmed_details(self, xml_content: str) -> List[PaperDTO]:
        """Parse detailed PubMed XML response."""
        papers = []
        
        try:
            from xml.etree import ElementTree as ET
            
            root = ET.fromstring(xml_content)
            articles = root.findall('.//PubmedArticle')
            
            for article in articles:
                try:
                    # Extract title
                    title_elem = article.find('.//ArticleTitle')
                    raw_title = title_elem.text if title_elem is not None else "Unknown Title"
                    title = clean_title(raw_title)
                    
                    # Extract DOI
                    doi_elem = article.find('.//ELocationID[@EIdType="doi"]')
                    doi = doi_elem.text if doi_elem is not None else None
                    
                    # Extract authors
                    authors = []
                    author_list = article.findall('.//Author')
                    for author in author_list:
                        first_name = author.find('ForeName')
                        last_name = author.find('LastName')
                        if first_name is not None and last_name is not None:
                            raw_name = f"{first_name.text} {last_name.text}"
                            clean_name = clean_author_name(raw_name)
                            if clean_name:
                                authors.append(AuthorDTO(name=clean_name))
                    
                    # Extract journal
                    journal_elem = article.find('.//Journal/Title')
                    raw_journal = journal_elem.text if journal_elem is not None else "Unknown Journal"
                    journal = clean_journal_name(raw_journal)
                    
                    # Extract abstract
                    abstract_elem = article.find('.//Abstract/AbstractText')
                    raw_abstract = abstract_elem.text if abstract_elem is not None else "No abstract available"
                    abstract = clean_abstract(raw_abstract)
                    
                    # Generate URLs if we have DOI
                    url = f"{self.base_url}/doi/full/{doi}" if doi else None
                    pdf_url = f"{self.base_url}/doi/pdf/{doi}" if doi else None
                    
                    paper = PaperDTO(
                        title=title,
                        doi=doi,
                        url=url,
                        pdf_url=pdf_url,
                        abstract=abstract,
                        authors=authors,
                        journal=journal,
                        source=PaperSource.ASM,
                        source_id=doi or title
                    )
                    papers.append(paper)
                    
                except Exception as e:
                    print(f"Error parsing individual PubMed article: {e}")
                    continue
                    
        except Exception as e:
            print(f"Error parsing PubMed details XML: {e}")
            
        return papers

    def _parse_crossref_asm_response(self, data: Dict[str, Any]) -> List[PaperDTO]:
        """Parse Crossref response specifically for ASM papers with improved filtering."""
        papers = []
        
        try:
            items = data.get('message', {}).get('items', [])
            
            for item in items:
                try:
                    # Filter for ASM papers - check multiple possible publisher variations
                    publisher = item.get('publisher', '').lower()
                    asm_indicators = [
                        'american society for microbiology',
                        'asm',
                        'microbiology society'  # Sometimes confused, but we'll filter this later
                    ]
                    
                    is_asm = any(indicator in publisher for indicator in asm_indicators)
                    
                    # Also check journal names for ASM journals
                    container_title = item.get('container-title', [])
                    journal_name = container_title[0].lower() if container_title else ''
                    
                    asm_journals = [
                        'msystems', 'mbio', 'msphere',
                        'applied and environmental microbiology',
                        'antimicrobial agents and chemotherapy',
                        'journal of bacteriology',
                        'journal of clinical microbiology',
                        'infection and immunity',
                        'journal of virology'
                    ]
                    
                    is_asm_journal = any(asm_journal in journal_name for asm_journal in asm_journals)
                    
                    # Only proceed if it's from ASM or an ASM journal
                    if not (is_asm or is_asm_journal):
                        continue
                    
                    # If it's "Microbiology Society" but not an ASM journal, skip it
                    if 'microbiology society' in publisher and not is_asm_journal:
                        continue
                    
                    # Extract title
                    title_list = item.get('title', [])
                    raw_title = title_list[0] if title_list else "Unknown Title"
                    
                    # Clean title using comprehensive text cleaner
                    title = clean_title(raw_title)
                    
                    # Extract DOI
                    doi = item.get('DOI', '')
                    
                    # Generate ASM URLs
                    url = f"{self.base_url}/doi/full/{doi}" if doi else None
                    pdf_url = f"{self.base_url}/doi/pdf/{doi}" if doi else None
                    
                    # Extract authors
                    authors = []
                    author_list = item.get('author', [])
                    for author in author_list:
                        given = author.get('given', '')
                        family = author.get('family', '')
                        if given and family:
                            raw_name = f"{given} {family}"
                        elif family:
                            raw_name = family
                        elif given:
                            raw_name = given
                        else:
                            continue
                        # Clean author name using text cleaner
                        clean_name = clean_author_name(raw_name)
                        if clean_name:  # Only add if name is not empty after cleaning
                            authors.append(AuthorDTO(name=clean_name))
                    
                    # Extract journal name
                    raw_journal = container_title[0] if container_title else "ASM Journal"
                    journal = clean_journal_name(raw_journal)
                    
                    # Extract publication date
                    pub_date = None
                    date_parts = item.get('published-print', {}).get('date-parts') or \
                                item.get('published-online', {}).get('date-parts')
                    if date_parts and len(date_parts) > 0 and len(date_parts[0]) >= 3:
                        year, month, day = date_parts[0][:3]
                        try:
                            pub_date = datetime(year, month, day)
                        except:
                            try:
                                pub_date = datetime(year, month, 1)
                            except:
                                try:
                                    pub_date = datetime(year, 1, 1)
                                except:
                                    pub_date = None
                    
                    # Extract abstract if available
                    raw_abstract = item.get('abstract', 'No abstract available')
                    abstract = clean_abstract(raw_abstract)
                    
                    paper = PaperDTO(
                        title=title,
                        doi=doi,
                        url=url,
                        pdf_url=pdf_url,
                        abstract=abstract,
                        authors=authors,
                        journal=journal,
                        publication_date=pub_date,
                        source=PaperSource.ASM,
                        source_id=doi or title
                    )
                    papers.append(paper)
                    
                except Exception as e:
                    print(f"Error parsing individual Crossref ASM result: {e}")
                    continue
                    
        except Exception as e:
            print(f"Error parsing Crossref ASM response: {e}")
            
        return papers

    def _parse_asm_response(self, html_content: str) -> List[PaperDTO]:
        """Parse ASM search results HTML."""
        papers = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Look for search result items
            result_items = soup.find_all('div', class_='al-article-item') or \
                          soup.find_all('div', class_='item__body') or \
                          soup.find_all('div', class_='searchResultItem')
            
            print(f"Found {len(result_items)} result items in ASM response")
            
            for item in result_items:
                try:
                    paper = self._parse_single_asm_result(item)
                    if paper:
                        papers.append(paper)
                        print(f"Successfully parsed ASM paper: {paper.title[:50]}...")
                except Exception as e:
                    print(f"Error parsing individual ASM result: {e}")
                    continue
                    
        except Exception as e:
            print(f"Error parsing ASM HTML response: {e}")
            
        return papers
    
    def _parse_single_asm_result(self, item) -> Optional[PaperDTO]:
        """Parse a single ASM search result item."""
        try:
            # Extract title
            title_elem = item.find('h5', class_='item__title') or \
                        item.find('a', class_='ref') or \
                        item.find('div', class_='hlFld-Title')
            
            if not title_elem:
                return None
                
            raw_title = title_elem.get_text(strip=True) if title_elem else "Unknown Title"
            title = clean_title(raw_title)
            
            # Extract URL
            url = None
            url_elem = title_elem.find('a') if title_elem.name != 'a' else title_elem
            if url_elem and url_elem.get('href'):
                url = url_elem['href']
                if url.startswith('/'):
                    url = f"{self.base_url}{url}"
            
            # Extract DOI from URL or text
            doi = None
            if url and '/doi/' in url:
                doi_match = re.search(r'/doi/(?:abs/|full/)?(.+?)(?:\?|$)', url)
                if doi_match:
                    doi = doi_match.group(1)
            
            # Look for DOI in text content
            if not doi:
                doi_text = item.get_text()
                doi = self.extract_doi(doi_text)
            
            # Extract authors
            authors = []
            authors_elem = item.find('div', class_='item__authors') or \
                         item.find('div', class_='contrib-authors') or \
                         item.find('div', class_='hlFld-Authors')
            
            if authors_elem:
                author_text = authors_elem.get_text(strip=True)
                # Split by common separators and clean up
                author_names = re.split(r',|;|\sand\s', author_text)
                for name in author_names:
                    cleaned_name = clean_author_name(name)
                    if cleaned_name and len(cleaned_name) > 2:
                        authors.append(AuthorDTO(name=cleaned_name))
            
            # Extract journal information
            journal = None
            journal_elem = item.find('div', class_='item__details') or \
                          item.find('div', class_='pub-history-title') or \
                          item.find('span', class_='citation__journal')
            
            if journal_elem:
                journal_text = journal_elem.get_text(strip=True)
                # Extract journal name from text like "mSystems. 2021; 6(4): e00489-21"
                journal_match = re.match(r'^([^.]+)', journal_text)
                if journal_match:
                    raw_journal = journal_match.group(1).strip()
                    journal = clean_journal_name(raw_journal)
            
            # Extract publication date
            pub_date = None
            date_elem = item.find('div', class_='item__date') or \
                       item.find('span', class_='pub-date')
            
            if date_elem:
                date_text = date_elem.get_text(strip=True)
                # Try to parse various date formats
                for date_format in ['%Y', '%b %Y', '%B %Y', '%d %b %Y', '%Y-%m-%d']:
                    try:
                        pub_date = datetime.strptime(date_text, date_format)
                        break
                    except:
                        continue
            
            # Extract abstract if available
            abstract = None
            abstract_elem = item.find('div', class_='item__abstract') or \
                           item.find('div', class_='hlFld-Abstract')
            
            if abstract_elem:
                raw_abstract = abstract_elem.get_text(strip=True)
                abstract = clean_abstract(raw_abstract)
            
            # Generate PDF URL if we have DOI
            pdf_url = None
            if doi:
                pdf_url = f"{self.base_url}/doi/pdf/{doi}"
            
            if title and (url or doi):
                return PaperDTO(
                    title=title,
                    doi=doi,
                    url=url,
                    pdf_url=pdf_url,
                    abstract=abstract,
                    authors=authors,
                    publication_date=pub_date,
                    journal=journal,
                    source=PaperSource.ASM,
                    source_id=doi
                )
                
        except Exception as e:
            print(f"Error parsing single ASM result: {e}")
            
        return None


class PlaywrightSearchService(BaseSearchService):
    """Service for browser-based searches using Playwright."""

    def __init__(self, session: aiohttp.ClientSession | None = None):
        super().__init__(session=session)
        self.browser = None
        self.context = None
        
    async def __aenter__(self):
        # Playwright is disabled for now
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={'User-Agent': self.user_agent.random}
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
    
    async def search(self, request: SearchRequestDTO) -> List[PaperDTO]:
        """Search method - currently returns empty as Playwright is disabled."""
        print("Playwright-based search temporarily disabled")
        return []


class SciHubService(BaseSearchService):
    """Service for downloading papers from Sci-Hub."""

    def __init__(self, session: aiohttp.ClientSession | None = None):
        super().__init__(session=session)
        self.base_urls = [
            'https://sci-hub.se/',
            'https://sci-hub.st/',
            'https://sci-hub.ru/',
        ]
    
    async def get_pdf_url(self, doi: str) -> Optional[str]:
        """Get PDF URL from Sci-Hub for a given DOI."""
        for base_url in self.base_urls:
            try:
                url = f"{base_url}{doi}"
                async with self.session.get(url, allow_redirects=True) as response:
                    if response.status == 200:
                        content = await response.text()
                        soup = BeautifulSoup(content, 'html.parser')
                        
                        # Look for PDF embed or direct link
                        pdf_embed = soup.find('embed', {'type': 'application/pdf'})
                        if pdf_embed and pdf_embed.get('src'):
                            pdf_url = pdf_embed['src']
                            if pdf_url.startswith('//'):
                                pdf_url = f"https:{pdf_url}"
                            elif pdf_url.startswith('/'):
                                pdf_url = f"{base_url.rstrip('/')}{pdf_url}"
                            return pdf_url
                        
                        # Look for iframe with PDF
                        pdf_iframe = soup.find('iframe', {'id': 'pdf'})
                        if pdf_iframe and pdf_iframe.get('src'):
                            pdf_url = pdf_iframe['src']
                            if pdf_url.startswith('//'):
                                pdf_url = f"https:{pdf_url}"
                            elif pdf_url.startswith('/'):
                                pdf_url = f"{base_url.rstrip('/')}{pdf_url}"
                            return pdf_url
                            
            except Exception as e:
                print(f"Error accessing Sci-Hub {base_url}: {e}")
                continue
                
        return None


class PaperSearchServiceFactory:
    """Factory for creating paper search services."""
    
    @staticmethod
    def create_service(source: PaperSource, *, session: aiohttp.ClientSession | None = None) -> BaseSearchService:
        """Create appropriate search service for the given source."""
        if source == PaperSource.ARXIV:
            return ArxivSearchService(session=session)
        elif source == PaperSource.CROSSREF:
            return CrossrefSearchService(session=session)
        elif source == PaperSource.ASM:
            return ASMSearchService(session=session)
        elif source == PaperSource.GOOGLE_SCHOLAR:
            return PlaywrightSearchService(session=session)
        elif source == PaperSource.IEEE:
            return PlaywrightSearchService(session=session)
        else:
            return BaseSearchService(session=session)
    
    @staticmethod
    async def search_all_sources(request: SearchRequestDTO) -> List[PaperDTO]:
        """Search all enabled sources for papers with ultra-fast parallel execution."""
        all_papers = []
        sources_to_search = request.sources if request.sources else [
            PaperSource.ARXIV,
            PaperSource.CROSSREF,
            PaperSource.ASM,
            PaperSource.GOOGLE_SCHOLAR,
            PaperSource.IEEE
        ]

        # Ultra-fast connector with aggressive settings
        connector = aiohttp.TCPConnector(
            limit=100,  # Increased connection pool
            limit_per_host=30,  # More connections per host
            ttl_dns_cache=300,  # DNS caching
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )

        # Reduced timeout for faster failure detection
        timeout = aiohttp.ClientTimeout(
            total=15,  # Reduced from 30s
            connect=3,  # Fast connection timeout
            sock_read=10  # Fast read timeout
        )

        async with aiohttp.ClientSession(
            timeout=timeout,
            connector=connector,
            headers={'User-Agent': UserAgent().random}
        ) as session:
            # Create tasks with timeout per source for ultra-fast execution
            tasks = []
            for source in sources_to_search:
                if source == PaperSource.GOOGLE_SCHOLAR:
                    tasks.append(asyncio.wait_for(
                        PaperSearchServiceFactory._search_google_scholar(request, session),
                        timeout=8  # Fast timeout for complex sources
                    ))
                elif source == PaperSource.IEEE:
                    tasks.append(asyncio.wait_for(
                        PaperSearchServiceFactory._search_ieee(request, session),
                        timeout=8
                    ))
                else:
                    tasks.append(asyncio.wait_for(
                        PaperSearchServiceFactory._search_basic(source, request, session),
                        timeout=12  # Slightly longer for API sources
                    ))

            # Use asyncio.as_completed for streaming results as they arrive
            results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for result in results:
            if isinstance(result, list):
                all_papers.extend(result)
            elif isinstance(result, Exception):
                print(f"Search error: {result}")
        
        # Remove duplicates based on DOI and title
        seen_dois = set()
        seen_titles = set()
        unique_papers = []
        
        for paper in all_papers:
            is_duplicate = False
            
            if paper.doi and paper.doi in seen_dois:
                is_duplicate = True
            elif paper.title.lower() in seen_titles:
                is_duplicate = True
            
            if not is_duplicate:
                if paper.doi:
                    seen_dois.add(paper.doi)
                seen_titles.add(paper.title.lower())
                unique_papers.append(paper)
        
        return unique_papers
    
    @staticmethod
    async def _search_basic(
        source: PaperSource,
        request: SearchRequestDTO,
        session: aiohttp.ClientSession
    ) -> List[PaperDTO]:
        """Search basic API-based sources using the shared session."""
        async with PaperSearchServiceFactory.create_service(source, session=session) as service:
            return await service.search(request)
    
    @staticmethod
    async def _search_google_scholar(request: SearchRequestDTO, session: aiohttp.ClientSession) -> List[PaperDTO]:
        """Search Google Scholar using Playwright - temporarily disabled."""
        print("Google Scholar search temporarily disabled - install Playwright to enable")
        return []
    
    @staticmethod
    async def _search_ieee(request: SearchRequestDTO, session: aiohttp.ClientSession) -> List[PaperDTO]:
        """Search IEEE using Playwright - temporarily disabled."""
        print("IEEE search temporarily disabled - install Playwright to enable")
        return [] 