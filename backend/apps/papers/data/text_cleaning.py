"""
Text cleaning utilities for processing research paper content from various sources.
Handles JATS XML, HTML tags, and formatting artifacts to provide clean, readable text.
"""

import re
import html
from typing import Dict, List, Optional, Callable
from enum import Enum
from dataclasses import dataclass


class CleaningLevel(Enum):
    """Text cleaning intensity levels."""
    BASIC = "basic"          # Remove only harmful tags, preserve formatting
    STANDARD = "standard"    # Remove most tags, normalize whitespace
    AGGRESSIVE = "aggressive" # Remove all markup, aggressive cleaning


class PublicationFormat(Enum):
    """Publication source formats that require different cleaning strategies."""
    JATS_XML = "jats_xml"    # JATS XML (Journal Article Tag Suite) - ASM, PubMed
    HTML = "html"            # General HTML content
    PLAIN_TEXT = "plain_text" # Plain text with minimal formatting
    AUTO_DETECT = "auto_detect" # Automatically detect format


@dataclass
class CleaningConfig:
    """Configuration for text cleaning operations."""
    level: CleaningLevel = CleaningLevel.STANDARD
    format: PublicationFormat = PublicationFormat.AUTO_DETECT
    preserve_italic: bool = True
    preserve_bold: bool = True
    preserve_superscript: bool = True
    preserve_subscript: bool = True
    normalize_whitespace: bool = True
    remove_citations: bool = False
    max_length: Optional[int] = None


class TextCleaner:
    """
    Comprehensive text cleaning utility for research paper content.
    Designed to handle various publication formats and cleaning requirements.
    """
    
    def __init__(self):
        self._init_patterns()
    
    def _init_patterns(self):
        """Initialize regex patterns for different types of content."""
        
        # JATS XML tags (Journal Article Tag Suite)
        self.jats_patterns = {
            # Content tags that should be removed but content preserved
            'content_tags': [
                r'<jats:p[^>]*?>(.*?)</jats:p>',
                r'<jats:title[^>]*?>(.*?)</jats:title>',
                r'<jats:sec[^>]*?>(.*?)</jats:sec>',
                r'<jats:abstract[^>]*?>(.*?)</jats:abstract>',
                r'<jats:named-content[^>]*?>(.*?)</jats:named-content>',
            ],
            # Formatting tags that should be converted to text equivalents
            'formatting_tags': {
                r'<jats:italic[^>]*?>(.*?)</jats:italic>': r'\1',  # Remove italics by default
                r'<jats:bold[^>]*?>(.*?)</jats:bold>': r'\1',      # Remove bold by default
                r'<jats:sup[^>]*?>(.*?)</jats:sup>': r'^\1',       # Convert to caret notation
                r'<jats:sub[^>]*?>(.*?)</jats:sub>': r'_\1',       # Convert to underscore notation
            },
            # Tags to remove completely
            'remove_tags': [
                r'<jats:xref[^>]*?>.*?</jats:xref>',  # Cross-references
                r'<jats:fig[^>]*?>.*?</jats:fig>',     # Figures
                r'<jats:table[^>]*?>.*?</jats:table>', # Tables
                r'<jats:fn[^>]*?>.*?</jats:fn>',       # Footnotes
            ]
        }
        
        # HTML patterns
        self.html_patterns = {
            'content_tags': [
                r'<p[^>]*?>(.*?)</p>',
                r'<div[^>]*?>(.*?)</div>',
                r'<span[^>]*?>(.*?)</span>',
                r'<section[^>]*?>(.*?)</section>',
                r'<article[^>]*?>(.*?)</article>',
            ],
            'formatting_tags': {
                r'<em[^>]*?>(.*?)</em>': r'\1',
                r'<i[^>]*?>(.*?)</i>': r'\1',
                r'<strong[^>]*?>(.*?)</strong>': r'\1',
                r'<b[^>]*?>(.*?)</b>': r'\1',
                r'<sup[^>]*?>(.*?)</sup>': r'^\1',
                r'<sub[^>]*?>(.*?)</sub>': r'_\1',
            },
            'remove_tags': [
                r'<script[^>]*?>.*?</script>',
                r'<style[^>]*?>.*?</style>',
                r'<nav[^>]*?>.*?</nav>',
                r'<header[^>]*?>.*?</header>',
                r'<footer[^>]*?>.*?</footer>',
            ]
        }
        
        # General patterns for all formats
        self.general_patterns = {
            # Remove line breaks and normalize whitespace
            'whitespace': [
                (r'\n+', ' '),           # Replace newlines with spaces
                (r'\r+', ' '),           # Replace carriage returns
                (r'\t+', ' '),           # Replace tabs
                (r'&nbsp;', ' '),        # Non-breaking spaces
                (r'\s{2,}', ' '),        # Multiple spaces to single space
            ],
            # Common markup artifacts
            'artifacts': [
                (r'&lt;', '<'),          # HTML entities
                (r'&gt;', '>'),
                (r'&amp;', '&'),
                (r'&quot;', '"'),
                (r'&apos;', "'"),
                (r'&#\d+;', ''),         # Numeric entities
                (r'&[a-zA-Z]+;', ''),    # Named entities (general removal)
            ],
            # Citation and reference patterns
            'citations': [
                r'\[\d+(?:,\s*\d+)*\]',  # [1], [1,2,3]
                r'\(\d+(?:,\s*\d+)*\)',  # (1), (1,2,3)
                r'\d+(?:,\s*\d+)*\s*\)', # Dangling citation numbers
            ]
        }
    
    def clean_text(self, text: str, config: CleaningConfig = None) -> str:
        """
        Main text cleaning method that applies appropriate cleaning based on configuration.
        
        Args:
            text: Raw text to clean
            config: Cleaning configuration (uses default if None)
            
        Returns:
            Cleaned text
        """
        if not text or not isinstance(text, str):
            return ""
        
        if config is None:
            config = CleaningConfig()
        
        # Decode HTML entities first
        text = html.unescape(text)
        
        # Detect format if auto-detect is enabled
        if config.format == PublicationFormat.AUTO_DETECT:
            config.format = self._detect_format(text)
        
        # Apply format-specific cleaning
        if config.format == PublicationFormat.JATS_XML:
            text = self._clean_jats_xml(text, config)
        elif config.format == PublicationFormat.HTML:
            text = self._clean_html(text, config)
        
        # Apply general cleaning
        text = self._clean_general(text, config)
        
        # Apply length limit if specified
        if config.max_length and len(text) > config.max_length:
            text = text[:config.max_length].rsplit(' ', 1)[0] + '...'
        
        return text.strip()
    
    def _detect_format(self, text: str) -> PublicationFormat:
        """Automatically detect the format of the input text."""
        if '<jats:' in text:
            return PublicationFormat.JATS_XML
        elif re.search(r'<[a-zA-Z][^>]*>', text):
            return PublicationFormat.HTML
        else:
            return PublicationFormat.PLAIN_TEXT
    
    def _clean_jats_xml(self, text: str, config: CleaningConfig) -> str:
        """Clean JATS XML specific tags and formatting."""
        
        # Remove complete tags first
        for pattern in self.jats_patterns['remove_tags']:
            text = re.sub(pattern, '', text, flags=re.DOTALL | re.IGNORECASE)
        
        # Handle content tags - extract content and remove tags (multiple passes for nested tags)
        max_iterations = 5  # Prevent infinite loops
        for _ in range(max_iterations):
            text_before = text
            for pattern in self.jats_patterns['content_tags']:
                text = re.sub(pattern, r'\1', text, flags=re.DOTALL | re.IGNORECASE)
            # If no changes were made, break
            if text == text_before:
                break
        
        # Handle formatting tags based on configuration
        for pattern, replacement in self.jats_patterns['formatting_tags'].items():
            if config.level == CleaningLevel.AGGRESSIVE:
                # Remove all formatting
                text = re.sub(pattern, r'\1', text, flags=re.DOTALL | re.IGNORECASE)
            elif config.level == CleaningLevel.STANDARD:
                # Apply simplified formatting
                text = re.sub(pattern, replacement, text, flags=re.DOTALL | re.IGNORECASE)
            # BASIC level preserves formatting tags
        
        # Clean up any remaining JATS tags that might have been missed
        if config.level == CleaningLevel.AGGRESSIVE:
            text = re.sub(r'<jats:[^>]*?>', '', text, flags=re.IGNORECASE)
            text = re.sub(r'</jats:[^>]*?>', '', text, flags=re.IGNORECASE)
        
        return text
    
    def _clean_html(self, text: str, config: CleaningConfig) -> str:
        """Clean HTML tags and formatting."""
        
        # Remove unwanted tags completely
        for pattern in self.html_patterns['remove_tags']:
            text = re.sub(pattern, '', text, flags=re.DOTALL | re.IGNORECASE)
        
        # Handle content tags
        for pattern in self.html_patterns['content_tags']:
            text = re.sub(pattern, r'\1', text, flags=re.DOTALL | re.IGNORECASE)
        
        # Handle formatting tags
        for pattern, replacement in self.html_patterns['formatting_tags'].items():
            if config.level == CleaningLevel.AGGRESSIVE:
                text = re.sub(pattern, r'\1', text, flags=re.DOTALL | re.IGNORECASE)
            elif config.level == CleaningLevel.STANDARD:
                text = re.sub(pattern, replacement, text, flags=re.DOTALL | re.IGNORECASE)
        
        # Remove any remaining HTML tags if aggressive cleaning
        if config.level == CleaningLevel.AGGRESSIVE:
            text = re.sub(r'<[^>]+>', '', text)
        
        return text
    
    def _clean_general(self, text: str, config: CleaningConfig) -> str:
        """Apply general cleaning patterns."""
        
        # Clean whitespace if configured
        if config.normalize_whitespace:
            for pattern, replacement in self.general_patterns['whitespace']:
                text = re.sub(pattern, replacement, text)
        
        # Clean artifacts
        for pattern, replacement in self.general_patterns['artifacts']:
            text = re.sub(pattern, replacement, text)
        
        # Remove citations if configured
        if config.remove_citations:
            for pattern in self.general_patterns['citations']:
                text = re.sub(pattern, '', text)
        
        return text
    
    # Convenience methods for specific fields
    def clean_title(self, title: str) -> str:
        """Clean paper title with title-specific configuration."""
        config = CleaningConfig(
            level=CleaningLevel.STANDARD,
            preserve_italic=False,
            preserve_bold=False,
            normalize_whitespace=True,
            max_length=500
        )
        return self.clean_text(title, config)
    
    def clean_abstract(self, abstract: str) -> str:
        """Clean paper abstract with abstract-specific configuration."""
        config = CleaningConfig(
            level=CleaningLevel.STANDARD,
            preserve_italic=True,
            preserve_bold=True,
            normalize_whitespace=True,
            remove_citations=True
        )
        return self.clean_text(abstract, config)
    
    def clean_author_name(self, name: str) -> str:
        """Clean author name with name-specific configuration."""
        config = CleaningConfig(
            level=CleaningLevel.AGGRESSIVE,
            normalize_whitespace=True,
            max_length=100
        )
        # Additional cleaning for author names
        cleaned = self.clean_text(name, config)
        
        # Remove common artifacts in author names
        cleaned = re.sub(r'[0-9*†‡§¶#+-]', '', cleaned)  # Remove superscript indicators
        cleaned = re.sub(r'\s+', ' ', cleaned)           # Normalize spaces
        
        return cleaned.strip()
    
    def clean_journal_name(self, journal: str) -> str:
        """Clean journal name with journal-specific configuration."""
        config = CleaningConfig(
            level=CleaningLevel.STANDARD,
            normalize_whitespace=True,
            max_length=300
        )
        return self.clean_text(journal, config)


# Singleton instance for global use
text_cleaner = TextCleaner()


# Convenience functions for backward compatibility and ease of use
def clean_title(title: str) -> str:
    """Clean paper title."""
    return text_cleaner.clean_title(title)


def clean_abstract(abstract: str) -> str:
    """Clean paper abstract."""
    return text_cleaner.clean_abstract(abstract)


def clean_author_name(name: str) -> str:
    """Clean author name."""
    return text_cleaner.clean_author_name(name)


def clean_journal_name(journal: str) -> str:
    """Clean journal name."""
    return text_cleaner.clean_journal_name(journal)


def clean_text_custom(text: str, 
                     level: CleaningLevel = CleaningLevel.STANDARD,
                     format: PublicationFormat = PublicationFormat.AUTO_DETECT,
                     **kwargs) -> str:
    """
    Clean text with custom configuration.
    
    Args:
        text: Text to clean
        level: Cleaning level (BASIC, STANDARD, AGGRESSIVE)
        format: Publication format (JATS_XML, HTML, PLAIN_TEXT, AUTO_DETECT)
        **kwargs: Additional configuration options
        
    Returns:
        Cleaned text
    """
    config = CleaningConfig(level=level, format=format, **kwargs)
    return text_cleaner.clean_text(text, config) 