"""
Repository implementations for data persistence and external API coordination.
These repositories implement the interfaces defined in the domain layer.
"""

import asyncio
import aiohttp
import os
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime
from pathlib import Path

from django.conf import settings
from django.core.cache import cache

from .models import (
    PaperDTO, SearchRequestDTO, SearchResultDTO, 
    DownloadTaskDTO, DownloadProgressDTO, SearchStatus, PaperSource
)
from .services import PaperSearchServiceFactory
from ..domain.models import Paper, SearchQuery, DownloadTask
from ..domain.interfaces import IPaperRepository, IDownloadTaskRepository


class PaperRepository(IPaperRepository):
    """Repository for managing paper search and download operations."""
    
    def __init__(self):
        self.download_dir = Path(settings.PAPERS_DOWNLOAD_DIR)
        self.download_dir.mkdir(exist_ok=True)
    
    async def search_papers(self, query: SearchQuery) -> List[Paper]:
        """Search for papers based on query."""
        # Convert string sources to PaperSource enums
        paper_sources = []
        for source_str in query.sources:
            try:
                # Map string source names to PaperSource enum values
                source_mapping = {
                    'arxiv': PaperSource.ARXIV,
                    'crossref': PaperSource.CROSSREF,
                    'asm': PaperSource.ASM,
                    'pubmed': PaperSource.PUBMED,
                    'ieee': PaperSource.IEEE,
                    'google_scholar': PaperSource.GOOGLE_SCHOLAR,
                    'scihub': PaperSource.SCIHUB,
                    'acm': PaperSource.ACM,
                    'springer': PaperSource.SPRINGER,
                    'elsevier': PaperSource.ELSEVIER,
                }
                
                if source_str in source_mapping:
                    paper_sources.append(source_mapping[source_str])
                else:
                    print(f"Unknown source: {source_str}")
                    
            except Exception as e:
                print(f"Error converting source {source_str}: {e}")
        
        # If no valid sources found, use default sources
        if not paper_sources:
            paper_sources = [PaperSource.ARXIV, PaperSource.CROSSREF, PaperSource.ASM]
            print(f"No valid sources found, using defaults: {paper_sources}")
        
        # Convert domain SearchQuery to DTO
        request_dto = SearchRequestDTO(
            query=query.query,
            sources=paper_sources,
            max_results=query.max_results
        )
        
        start_time = datetime.utcnow()
        
        try:
            print(f"Searching with sources: {[s.value for s in request_dto.sources]}")
            papers_dtos = await PaperSearchServiceFactory.search_all_sources(request_dto)
            print(f"Found {len(papers_dtos)} papers from factory")
            
            # Convert DTOs to domain objects
            papers = []
            for paper_dto in papers_dtos:
                # Extract DOI if not present
                doi = paper_dto.doi
                if not doi and paper_dto.abstract:
                    doi = self._extract_doi_from_text(paper_dto.abstract)
                
                paper = Paper(
                    title=paper_dto.title,
                    doi=doi,
                    url=paper_dto.url,
                    pdf_url=paper_dto.pdf_url,
                    abstract=paper_dto.abstract,
                    authors=[author.name for author in paper_dto.authors],
                    publication_date=paper_dto.publication_date,
                    journal=paper_dto.journal,
                    keywords=paper_dto.keywords,
                    source=paper_dto.source.value if paper_dto.source else None
                )
                papers.append(paper)
            
            print(f"Converted {len(papers)} papers to domain objects")
            return papers
            
        except Exception as e:
            print(f"Error searching papers: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def _extract_doi_from_text(self, text: str) -> Optional[str]:
        """Extract DOI from text using regex."""
        import re
        doi_pattern = r'10\.\d{4,}[^\s]*[^\s\.,]'
        match = re.search(doi_pattern, text)
        return match.group(0) if match else None
    
    async def download_paper_pdf(self, paper: Paper) -> Optional[str]:
        """Download PDF for a single paper."""
        if not paper.pdf_url:
            return None
        
        try:
            # Create filename
            safe_title = "".join(c for c in paper.title if c.isalnum() or c in (' ', '-', '_')).strip()
            safe_title = safe_title[:100]  # Limit length
            filename = f"{safe_title}_{uuid.uuid4().hex[:8]}.pdf"
            filepath = self.download_dir / filename
            
            # Download PDF
            async with aiohttp.ClientSession() as session:
                async with session.get(paper.pdf_url) as response:
                    if response.status == 200:
                        with open(filepath, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                f.write(chunk)
                        return str(filepath)
            
        except Exception as e:
            print(f"Error downloading PDF for {paper.title}: {e}")
            
        return None
    
    def get_available_sources(self) -> List[Dict[str, Any]]:
        """Get list of available paper sources."""
        return [
            {
                'id': 'arxiv',
                'name': 'arXiv',
                'description': 'Open access repository of scientific papers',
                'enabled': True
            },
            {
                'id': 'crossref',
                'name': 'Crossref',
                'description': 'Digital Object Identifier (DOI) registry',
                'enabled': True
            },
            {
                'id': 'asm',
                'name': 'ASM Journals',
                'description': 'American Society for Microbiology journal publications',
                'enabled': True
            },
            {
                'id': 'google_scholar',
                'name': 'Google Scholar',
                'description': 'Academic search engine',
                'enabled': True
            },
            {
                'id': 'ieee',
                'name': 'IEEE Xplore',
                'description': 'IEEE digital library',
                'enabled': True
            },
            {
                'id': 'pubmed',
                'name': 'PubMed',
                'description': 'Medical and life sciences literature',
                'enabled': True
            },
            {
                'id': 'scihub',
                'name': 'Sci-Hub',
                'description': 'Free access to scientific papers',
                'enabled': True
            }
        ]


class DownloadTaskRepository(IDownloadTaskRepository):
    """Repository for managing download tasks."""
    
    def create_task(self, papers: List[Paper]) -> DownloadTask:
        """Create a new download task."""
        task_id = str(uuid.uuid4())
        task = DownloadTask(
            task_id=task_id,
            papers=papers,
            status='pending'
        )
        
        # Store task in cache for active tracking
        self._store_task(task)
        
        # Add to active tasks list
        active_tasks = cache.get('active_download_tasks', [])
        active_tasks.append(task_id)
        cache.set('active_download_tasks', active_tasks, timeout=3600 * 24)  # 24 hours
        
        return task
    
    def get_task(self, task_id: str) -> Optional[DownloadTask]:
        """Get download task by ID."""
        return self._retrieve_task(task_id)
    
    def update_task(self, task: DownloadTask) -> None:
        """Update download task."""
        self._store_task(task)
        
        # If task is completed or failed, remove from active list and add to completed list
        if task.status in ['completed', 'failed']:
            active_tasks = cache.get('active_download_tasks', [])
            if task.task_id in active_tasks:
                active_tasks.remove(task.task_id)
                cache.set('active_download_tasks', active_tasks, timeout=3600 * 24)
            
            # Add to completed tasks list
            completed_tasks = cache.get('completed_download_tasks', [])
            if task.task_id not in completed_tasks:
                completed_tasks.append(task.task_id)
                cache.set('completed_download_tasks', completed_tasks, timeout=3600 * 24 * 7)  # Keep for 7 days
    
    def get_active_tasks(self) -> List[DownloadTask]:
        """Get all active download tasks."""
        active_task_ids = cache.get('active_download_tasks', [])
        active_tasks = []
        
        for task_id in active_task_ids:
            task = self._retrieve_task(task_id)
            if task and task.status in ['pending', 'in_progress', 'downloading']:
                active_tasks.append(task)
        
        return active_tasks
    
    def get_all_tasks(self) -> List[DownloadTask]:
        """Get all download tasks (active, completed, and failed)."""
        # Get all task IDs from cache (both active and completed)
        active_task_ids = cache.get('active_download_tasks', [])
        completed_task_ids = cache.get('completed_download_tasks', [])
        all_task_ids = set(active_task_ids + completed_task_ids)
        
        all_tasks = []
        
        for task_id in all_task_ids:
            task = self._retrieve_task(task_id)
            if task:
                all_tasks.append(task)
        
        return all_tasks
    
    def _store_task(self, task: DownloadTask) -> None:
        """Store task in cache."""
        # Handle started_at and completed_at timestamps
        started_at_str = None
        if hasattr(task, 'started_at') and task.started_at:
            started_at_str = task.started_at.isoformat()
        
        completed_at_str = None
        if hasattr(task, 'completed_at') and task.completed_at:
            completed_at_str = task.completed_at.isoformat()
            
        created_at_str = datetime.utcnow().isoformat()
        if hasattr(task, 'created_at') and task.created_at:
            created_at_str = task.created_at.isoformat()
        
        task_data = {
            'task_id': task.task_id,
            'papers': [self._paper_to_dict(p) for p in task.papers],
            'status': task.status,
            'downloaded_count': getattr(task, 'downloaded_count', 0),
            'failed_count': getattr(task, 'failed_count', 0),
            'progress': getattr(task, 'progress', 0),
            'created_at': created_at_str,
            'started_at': started_at_str,
            'completed_at': completed_at_str,
            'error_message': getattr(task, 'error_message', None)
        }
        cache.set(f"download_task_{task.task_id}", task_data, timeout=3600 * 24)  # 24 hours
    
    def _retrieve_task(self, task_id: str) -> Optional[DownloadTask]:
        """Retrieve task from cache."""
        task_data = cache.get(f"download_task_{task_id}")
        if not task_data:
            return None
        
        # Convert back to domain object
        papers = [self._dict_to_paper(p) for p in task_data['papers']]
        task = DownloadTask(
            task_id=task_data['task_id'],
            papers=papers,
            status=task_data['status']
        )
        
        # Set additional attributes
        task.downloaded_count = task_data.get('downloaded_count', 0)
        task.failed_count = task_data.get('failed_count', 0)
        task.progress = task_data.get('progress', 0)
        task.error_message = task_data.get('error_message')
        
        # Parse timestamps
        if task_data.get('created_at'):
            task.created_at = datetime.fromisoformat(task_data['created_at'].replace('Z', '+00:00'))
        if task_data.get('started_at'):
            task.started_at = datetime.fromisoformat(task_data['started_at'].replace('Z', '+00:00'))
        if task_data.get('completed_at'):
            task.completed_at = datetime.fromisoformat(task_data['completed_at'].replace('Z', '+00:00'))
        
        return task
    
    def _paper_to_dict(self, paper: Paper) -> Dict[str, Any]:
        """Convert Paper to dictionary for storage."""
        return {
            'title': paper.title,
            'doi': paper.doi,
            'url': paper.url,
            'pdf_url': paper.pdf_url,
            'abstract': paper.abstract,
            'authors': paper.authors,
            'publication_date': paper.publication_date.isoformat() if paper.publication_date else None,
            'journal': paper.journal,
            'keywords': paper.keywords,
            'source': paper.source
        }
    
    def _dict_to_paper(self, paper_dict: Dict[str, Any]) -> Paper:
        """Convert dictionary to Paper domain object."""
        publication_date = None
        if paper_dict.get('publication_date'):
            try:
                publication_date = datetime.fromisoformat(paper_dict['publication_date'].replace('Z', '+00:00'))
            except:
                pass
        
        return Paper(
            title=paper_dict['title'],
            doi=paper_dict.get('doi'),
            url=paper_dict.get('url'),
            pdf_url=paper_dict.get('pdf_url'),
            abstract=paper_dict.get('abstract'),
            authors=paper_dict.get('authors', []),
            publication_date=publication_date,
            journal=paper_dict.get('journal'),
            keywords=paper_dict.get('keywords', []),
            source=paper_dict.get('source')
        )

    def _paper_to_dto(self, paper: Paper) -> PaperDTO:
        """Convert domain Paper to PaperDTO."""
        return PaperDTO(
            title=paper.title,
            doi=paper.doi,
            url=paper.url,
            pdf_url=paper.pdf_url,
            abstract=paper.abstract,
            authors=paper.authors,
            publication_date=paper.publication_date,
            journal=paper.journal,
            keywords=paper.keywords,
            source=paper.source
        )
    
    def _dto_to_paper(self, paper_dto: PaperDTO) -> Paper:
        """Convert PaperDTO to domain Paper."""
        return Paper(
            title=paper_dto.title,
            doi=paper_dto.doi,
            url=paper_dto.url,
            pdf_url=paper_dto.pdf_url,
            abstract=paper_dto.abstract,
            authors=paper_dto.authors,
            publication_date=paper_dto.publication_date,
            journal=paper_dto.journal,
            keywords=paper_dto.keywords,
            source=paper_dto.source
        ) 