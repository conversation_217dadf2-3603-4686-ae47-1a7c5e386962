"""
Data models (DTOs) for paper downloader.
These are data transfer objects used for API communication and data serialization.
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class SearchStatus(Enum):
    PENDING = "pending"
    SEARCHING = "searching"
    DOWNLOADING = "downloading"
    COMPLETED = "completed"
    FAILED = "failed"


class PaperSource(Enum):
    ARXIV = "arxiv"
    PUBMED = "pubmed"
    IEEE = "ieee"
    ACM = "acm"
    SPRINGER = "springer"
    ELSEVIER = "elsevier"
    SCIHUB = "scihub"
    GOOGLE_SCHOLAR = "google_scholar"
    CROSSREF = "crossref"
    ASM = "asm"


@dataclass
class AuthorDTO:
    """Data transfer object for paper authors."""
    name: str
    affiliation: Optional[str] = None
    email: Optional[str] = None


@dataclass
class PaperDTO:
    """Data transfer object for research papers."""
    title: str
    doi: Optional[str] = None
    url: Optional[str] = None
    pdf_url: Optional[str] = None
    abstract: Optional[str] = None
    authors: List[AuthorDTO] = field(default_factory=list)
    publication_date: Optional[datetime] = None
    journal: Optional[str] = None
    volume: Optional[str] = None
    issue: Optional[str] = None
    pages: Optional[str] = None
    keywords: List[str] = field(default_factory=list)
    source: Optional[PaperSource] = None
    source_id: Optional[str] = None
    citations_count: Optional[int] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SearchRequestDTO:
    """Data transfer object for search requests."""
    query: str
    max_results: int = 50
    sources: List[PaperSource] = field(default_factory=list)
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    filters: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SearchResultDTO:
    """Data transfer object for search results."""
    papers: List[PaperDTO] = field(default_factory=list)
    total_found: int = 0
    search_time: float = 0.0
    sources_searched: List[PaperSource] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)


@dataclass
class DownloadTaskDTO:
    """Data transfer object for download tasks."""
    task_id: str
    papers: List[PaperDTO] = field(default_factory=list)
    status: SearchStatus = SearchStatus.PENDING
    progress: int = 0
    total_papers: int = 0
    downloaded_papers: int = 0
    failed_downloads: int = 0
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    error_message: Optional[str] = None


@dataclass
class DownloadProgressDTO:
    """Data transfer object for download progress updates."""
    task_id: str
    status: SearchStatus
    progress: int
    current_paper: Optional[str] = None
    message: Optional[str] = None
    downloaded_count: int = 0
    failed_count: int = 0
    total_count: int = 0 