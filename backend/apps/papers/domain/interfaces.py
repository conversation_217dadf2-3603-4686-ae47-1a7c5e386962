"""
Repository interfaces for the paper downloader domain.
These interfaces define contracts for data access and external services.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any

from .models import Paper, SearchQuery, DownloadTask


class IPaperRepository(ABC):
    """Interface for paper data operations."""
    
    @abstractmethod
    async def search_papers(self, query: SearchQuery) -> List[Paper]:
        """Search for papers based on query."""
        pass
    
    @abstractmethod
    async def download_paper_pdf(self, paper: Paper) -> Optional[str]:
        """Download PDF for a paper and return file path."""
        pass
    
    @abstractmethod
    def get_available_sources(self) -> List[Dict[str, Any]]:
        """Get list of available paper sources."""
        pass


class IDownloadTaskRepository(ABC):
    """Interface for download task operations."""
    
    @abstractmethod
    def create_task(self, papers: List[Paper]) -> DownloadTask:
        """Create a new download task."""
        pass
    
    @abstractmethod
    def get_task(self, task_id: str) -> Optional[DownloadTask]:
        """Get download task by ID."""
        pass
    
    @abstractmethod
    def update_task(self, task: DownloadTask) -> None:
        """Update download task."""
        pass
    
    @abstractmethod
    def get_active_tasks(self) -> List[DownloadTask]:
        """Get all active download tasks."""
        pass


class INotificationService(ABC):
    """Interface for sending real-time notifications."""
    
    @abstractmethod
    async def send_search_progress(self, query_id: str, progress: Dict[str, Any]) -> None:
        """Send search progress notification."""
        pass
    
    @abstractmethod
    async def send_download_progress(self, task_id: str, progress: Dict[str, Any]) -> None:
        """Send download progress notification."""
        pass
    
    @abstractmethod
    async def send_task_completed(self, task_id: str, result: Dict[str, Any]) -> None:
        """Send task completion notification."""
        pass 