"""
Domain models (Business entities) for the paper downloader.
These represent the core business concepts and rules.
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class Paper:
    """Business entity representing a research paper."""
    
    def __init__(
        self,
        title: str,
        doi: Optional[str] = None,
        url: Optional[str] = None,
        pdf_url: Optional[str] = None,
        abstract: Optional[str] = None,
        authors: List[str] = None,
        publication_date: Optional[datetime] = None,
        journal: Optional[str] = None,
        keywords: List[str] = None,
        source: Optional[str] = None
    ):
        if not title:
            raise ValueError("Paper title is required")
        
        self.title = title.strip()
        self.doi = doi
        self.url = url
        self.pdf_url = pdf_url
        self.abstract = abstract
        self.authors = authors or []
        self.publication_date = publication_date
        self.journal = journal
        self.keywords = keywords or []
        self.source = source
        
        # Business rules
        self._validate()
    
    def _validate(self):
        """Validate business rules for paper entity."""
        if len(self.title) > 500:
            raise ValueError("Paper title cannot exceed 500 characters")
        
        if self.doi and not self._is_valid_doi(self.doi):
            raise ValueError(f"Invalid DOI format: {self.doi}")
        
        if self.url and not self._is_valid_url(self.url):
            raise ValueError(f"Invalid URL format: {self.url}")
    
    def _is_valid_doi(self, doi: str) -> bool:
        """Validate DOI format."""
        import re
        doi_pattern = r'^10\.\d{4,}/[^\s]+$'
        return bool(re.match(doi_pattern, doi))
    
    def _is_valid_url(self, url: str) -> bool:
        """Validate URL format."""
        import re
        url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        return bool(re.match(url_pattern, url, re.IGNORECASE))
    
    def has_pdf_access(self) -> bool:
        """Check if paper has accessible PDF."""
        return bool(self.pdf_url)
    
    def get_citation_key(self) -> str:
        """Generate a citation key for the paper."""
        if self.authors:
            first_author = self.authors[0].split()[-1]  # Last name
        else:
            first_author = "Unknown"
        
        year = ""
        if self.publication_date:
            year = str(self.publication_date.year)
        
        # Create short title
        title_words = self.title.split()[:3]
        short_title = "".join(word.capitalize() for word in title_words)
        
        return f"{first_author}{year}{short_title}"
    
    def __str__(self):
        return f"Paper(title='{self.title[:50]}...', doi='{self.doi}')"
    
    def __eq__(self, other):
        if not isinstance(other, Paper):
            return False
        
        # Papers are equal if they have the same DOI or same title
        if self.doi and other.doi:
            return self.doi == other.doi
        
        return self.title.lower().strip() == other.title.lower().strip()
    
    def __hash__(self):
        return hash((self.doi, self.title.lower().strip()))


class SearchQuery:
    """Business entity representing a search query."""
    
    def __init__(
        self,
        query: str,
        max_results: int = 50,
        sources: List[str] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None
    ):
        if not query or not query.strip():
            raise ValueError("Search query cannot be empty")
        
        if max_results <= 0 or max_results > 1000:
            raise ValueError("Max results must be between 1 and 1000")
        
        self.query = query.strip()
        self.max_results = max_results
        self.sources = sources or []
        self.date_from = date_from
        self.date_to = date_to
        
        # Validate date range
        if self.date_from and self.date_to and self.date_from > self.date_to:
            raise ValueError("Date from cannot be after date to")
    
    def is_valid_for_source(self, source: str) -> bool:
        """Check if query is valid for a specific source."""
        if not self.sources:
            return True  # No source restriction
        
        return source in self.sources
    
    def get_formatted_query(self, source: str) -> str:
        """Get query formatted for specific source."""
        # Basic implementation - can be extended for source-specific formatting
        return self.query
    
    def __str__(self):
        return f"SearchQuery(query='{self.query}', max_results={self.max_results})"


class DownloadTask:
    """Business entity representing a bulk download task."""
    
    def __init__(self, task_id: str, papers: List[Paper], status: str = "pending"):
        if not papers:
            raise ValueError("Download task must have at least one paper")
        
        if len(papers) > 500:
            raise ValueError("Cannot download more than 500 papers in a single task")
        
        self.task_id = task_id
        self.papers = papers
        self.status = status
        self.progress = 0
        self.downloaded_count = 0
        self.failed_count = 0
        self.created_at = datetime.utcnow()
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        self.error_message: Optional[str] = None
    
    def _generate_task_id(self) -> str:
        """Generate unique task ID."""
        import uuid
        return str(uuid.uuid4())
    
    def start(self):
        """Mark task as started."""
        if self.status != "pending":
            raise ValueError(f"Cannot start task in {self.status} status")
        
        self.status = "downloading"
        self.started_at = datetime.utcnow()
    
    def update_progress(self, downloaded: int, failed: int):
        """Update download progress."""
        self.downloaded_count = downloaded
        self.failed_count = failed
        total_processed = downloaded + failed
        self.progress = int((total_processed / len(self.papers)) * 100)
    
    def complete(self):
        """Mark task as completed."""
        self.status = "completed"
        self.completed_at = datetime.utcnow()
        self.progress = 100
    
    def fail(self, error_message: str):
        """Mark task as failed."""
        self.status = "failed"
        self.error_message = error_message
        self.completed_at = datetime.utcnow()
    
    def get_success_rate(self) -> float:
        """Get success rate as percentage."""
        if self.downloaded_count + self.failed_count == 0:
            return 0.0
        
        return (self.downloaded_count / (self.downloaded_count + self.failed_count)) * 100
    
    def get_duration(self) -> Optional[float]:
        """Get task duration in seconds."""
        if not self.started_at:
            return None
        
        end_time = self.completed_at or datetime.utcnow()
        return (end_time - self.started_at).total_seconds()
    
    def is_active(self) -> bool:
        """Check if task is currently active."""
        return self.status in ["pending", "downloading"]
    
    def __str__(self):
        return f"DownloadTask(id='{self.task_id}', papers={len(self.papers)}, status='{self.status}')" 