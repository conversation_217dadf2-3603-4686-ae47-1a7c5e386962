"""
Use cases for the paper downloader application.
These encapsulate the business logic and orchestrate domain operations.
"""

import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime

from .models import Paper, SearchQuery, DownloadTask
from .interfaces import IPaperRepository, IDownloadTaskRepository, INotificationService


class SearchPapersUseCase:
    """Use case for searching papers across multiple sources."""
    
    def __init__(self, paper_repository: IPaperRepository, notification_service: INotificationService):
        self.paper_repository = paper_repository
        self.notification_service = notification_service
    
    async def execute(
        self, 
        query: str, 
        max_results: int = 50, 
        sources: List[str] = None
    ) -> List[Paper]:
        """
        Execute paper search use case.
        
        Args:
            query: Search query string
            max_results: Maximum number of results to return
            sources: List of sources to search (optional)
        
        Returns:
            List of papers found
        
        Raises:
            ValueError: If query is invalid
        """
        # Create search query domain object (includes validation)
        search_query = SearchQuery(
            query=query,
            max_results=max_results,
            sources=sources
        )
        
        # Generate unique query ID for progress tracking
        import uuid
        query_id = str(uuid.uuid4())
        
        # Send initial progress notification
        await self.notification_service.send_search_progress(
            query_id, 
            {"status": "started", "query": query, "sources": sources or []}
        )
        
        try:
            # Execute search
            papers = await self.paper_repository.search_papers(search_query)
            
            # Send completion notification
            await self.notification_service.send_search_progress(
                query_id,
                {
                    "status": "completed",
                    "found": len(papers),
                    "query": query
                }
            )
            
            return papers
            
        except Exception as e:
            # Send error notification
            await self.notification_service.send_search_progress(
                query_id,
                {
                    "status": "failed",
                    "error": str(e),
                    "query": query
                }
            )
            raise


class BulkDownloadUseCase:
    """Use case for bulk downloading papers."""
    
    def __init__(
        self, 
        paper_repository: IPaperRepository,
        task_repository: IDownloadTaskRepository,
        notification_service: INotificationService
    ):
        self.paper_repository = paper_repository
        self.task_repository = task_repository
        self.notification_service = notification_service
    
    def create_download_task(self, papers: List[Dict[str, Any]]) -> DownloadTask:
        """
        Create a new download task.
        
        Args:
            papers: List of paper data dictionaries
        
        Returns:
            Created download task
        
        Raises:
            ValueError: If papers list is invalid
        """
        if not papers:
            raise ValueError("Cannot create download task with empty papers list")
        
        # Convert paper dictionaries to domain objects
        paper_objects = []
        for paper_data in papers:
            try:
                paper = Paper(
                    title=paper_data['title'],
                    doi=paper_data.get('doi'),
                    url=paper_data.get('url'),
                    pdf_url=paper_data.get('pdf_url'),
                    abstract=paper_data.get('abstract'),
                    authors=paper_data.get('authors', []),
                    publication_date=paper_data.get('publication_date'),
                    journal=paper_data.get('journal'),
                    keywords=paper_data.get('keywords', []),
                    source=paper_data.get('source')
                )
                paper_objects.append(paper)
            except Exception as e:
                # Log error but continue with other papers
                print(f"Error creating paper object: {e}")
                continue
        
        if not paper_objects:
            raise ValueError("No valid papers found in the provided data")
        
        # Create download task
        task = self.task_repository.create_task(paper_objects)
        
        return task
    
    async def execute_download_task(self, task_id: str) -> DownloadTask:
        """
        Execute a download task.
        
        Args:
            task_id: ID of the task to execute
        
        Returns:
            Updated download task
        
        Raises:
            ValueError: If task not found or invalid
        """
        # Get task
        task = self.task_repository.get_task(task_id)
        if not task:
            raise ValueError(f"Download task {task_id} not found")
        
        if not task.is_active():
            raise ValueError(f"Task {task_id} is not in an active state")
        
        # Start task
        task.start()
        self.task_repository.update_task(task)
        
        # Send initial progress notification
        await self.notification_service.send_download_progress(
            task_id,
            {
                "status": task.status,
                "progress": task.progress,
                "downloaded": task.downloaded_count,
                "failed": task.failed_count,
                "total": len(task.papers)
            }
        )
        
        # Download papers concurrently (limit concurrency)
        downloaded_count = 0
        failed_count = 0
        semaphore = asyncio.Semaphore(5)  # Limit to 5 concurrent downloads
        
        async def download_single_paper(paper: Paper) -> bool:
            """Download a single paper with semaphore protection."""
            nonlocal downloaded_count, failed_count
            
            async with semaphore:
                try:
                    # Send progress update for current paper
                    await self.notification_service.send_download_progress(
                        task_id,
                        {
                            "status": "downloading",
                            "current_paper": paper.title,
                            "progress": task.progress,
                            "downloaded": downloaded_count,
                            "failed": failed_count,
                            "total": len(task.papers)
                        }
                    )
                    
                    # Attempt download
                    file_path = await self.paper_repository.download_paper_pdf(paper)
                    
                    if file_path:
                        downloaded_count += 1
                        return True
                    else:
                        failed_count += 1
                        return False
                        
                except Exception as e:
                    print(f"Error downloading {paper.title}: {e}")
                    failed_count += 1
                    return False
                finally:
                    # Update task progress
                    task.update_progress(downloaded_count, failed_count)
                    self.task_repository.update_task(task)
                    
                    # Send progress notification
                    await self.notification_service.send_download_progress(
                        task_id,
                        {
                            "status": task.status,
                            "progress": task.progress,
                            "downloaded": downloaded_count,
                            "failed": failed_count,
                            "total": len(task.papers)
                        }
                    )
        
        try:
            # Execute downloads concurrently
            download_tasks = [download_single_paper(paper) for paper in task.papers]
            await asyncio.gather(*download_tasks, return_exceptions=True)
            
            # Complete task
            task.complete()
            self.task_repository.update_task(task)
            
            # Send completion notification
            await self.notification_service.send_task_completed(
                task_id,
                {
                    "status": task.status,
                    "downloaded": task.downloaded_count,
                    "failed": task.failed_count,
                    "total": len(task.papers),
                    "success_rate": task.get_success_rate(),
                    "duration": task.get_duration()
                }
            )
            
            return task
            
        except Exception as e:
            # Mark task as failed
            task.fail(str(e))
            self.task_repository.update_task(task)
            
            # Send failure notification
            await self.notification_service.send_task_completed(
                task_id,
                {
                    "status": task.status,
                    "error": str(e),
                    "downloaded": task.downloaded_count,
                    "failed": task.failed_count,
                    "total": len(task.papers)
                }
            )
            
            raise


class GetDownloadTaskStatusUseCase:
    """Use case for getting download task status."""
    
    def __init__(self, task_repository: IDownloadTaskRepository):
        self.task_repository = task_repository
    
    def execute(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get download task status.
        
        Args:
            task_id: ID of the task
        
        Returns:
            Task status information or None if not found
        """
        task = self.task_repository.get_task(task_id)
        if not task:
            return None
        
        return {
            "task_id": task.task_id,
            "status": task.status,
            "progress": task.progress,
            "downloaded_count": task.downloaded_count,
            "failed_count": task.failed_count,
            "total_papers": len(task.papers),
            "success_rate": task.get_success_rate(),
            "duration": task.get_duration(),
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "error_message": task.error_message
        }


class GetAvailableSourcesUseCase:
    """Use case for getting available paper sources."""
    
    def __init__(self, paper_repository: IPaperRepository):
        self.paper_repository = paper_repository
    
    def execute(self) -> List[Dict[str, Any]]:
        """
        Get list of available paper sources.
        
        Returns:
            List of available sources with metadata
        """
        return self.paper_repository.get_available_sources() 