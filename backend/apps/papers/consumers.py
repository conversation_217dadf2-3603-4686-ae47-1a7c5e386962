"""
WebSocket consumers for real-time communication.
"""

import json
from channels.generic.websocket import AsyncWebsocketConsumer


class SearchProgressConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for search progress updates."""
    
    async def connect(self):
        """Handle WebSocket connection."""
        self.query_id = self.scope['url_route']['kwargs']['query_id']
        self.group_name = f'search_{self.query_id}'
        
        # Join group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await self.accept()
    
    async def disconnect(self, close_code):
        """Handle WebSocket disconnection."""
        # Leave group
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )
    
    async def search_progress(self, event):
        """Handle search progress messages."""
        progress = event['progress']
        
        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'search_progress',
            'progress': progress
        }))


class DownloadProgressConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for download progress updates."""
    
    async def connect(self):
        """Handle WebSocket connection."""
        # Handle both specific task connections and general downloads connection
        if 'task_id' in self.scope['url_route']['kwargs']:
            self.task_id = self.scope['url_route']['kwargs']['task_id']
            self.group_name = f'download_{self.task_id}'
        else:
            # General downloads connection - can join multiple task groups
            self.task_id = None
            self.group_name = 'downloads_general'
            
        # Join group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await self.accept()
    
    async def disconnect(self, close_code):
        """Handle WebSocket disconnection."""
        # Leave all groups this connection might be in
        if self.task_id:
            await self.channel_layer.group_discard(
                f'download_{self.task_id}',
                self.channel_name
            )
        else:
            await self.channel_layer.group_discard(
                self.group_name,
                self.channel_name
            )
    
    async def receive(self, text_data):
        """Handle messages from WebSocket."""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'join_task':
                task_id = data.get('data', {}).get('task_id')
                if task_id:
                    group_name = f'download_{task_id}'
                    await self.channel_layer.group_add(group_name, self.channel_name)
                    await self.send(text_data=json.dumps({
                        'type': 'joined_task',
                        'data': {'task_id': task_id}
                    }))
            
            elif message_type == 'leave_task':
                task_id = data.get('data', {}).get('task_id')
                if task_id:
                    group_name = f'download_{task_id}'
                    await self.channel_layer.group_discard(group_name, self.channel_name)
                    await self.send(text_data=json.dumps({
                        'type': 'left_task',
                        'data': {'task_id': task_id}
                    }))
                    
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON message'
            }))
    
    async def download_progress(self, event):
        """Handle download progress messages."""
        progress = event['progress']
        
        # Send message to WebSocket in the format frontend expects
        await self.send(text_data=json.dumps({
            'type': 'download_progress',
            'data': progress
        }))
    
    async def task_completed(self, event):
        """Handle task completion messages."""
        result = event['result']
        
        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'task_completed',
            'data': result
        })) 