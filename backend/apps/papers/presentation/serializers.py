"""
Serializers for the papers API.
These handle request/response serialization for the REST API.
"""

from rest_framework import serializers
from datetime import datetime
from typing import List, Dict, Any


class AuthorSerializer(serializers.Serializer):
    """Serializer for paper authors."""
    name = serializers.Char<PERSON>ield(max_length=200)
    affiliation = serializers.CharField(max_length=500, required=False, allow_blank=True)
    email = serializers.EmailField(required=False, allow_blank=True)


class PaperSerializer(serializers.Serializer):
    """Serializer for research papers."""
    title = serializers.CharField(max_length=500)
    doi = serializers.CharField(max_length=200, required=False, allow_blank=True)
    url = serializers.URLField(required=False, allow_blank=True)
    pdf_url = serializers.URLField(required=False, allow_blank=True)
    abstract = serializers.CharField(required=False, allow_blank=True)
    authors = AuthorSerializer(many=True, required=False)
    publication_date = serializers.DateTimeField(required=False, allow_null=True)
    journal = serializers.CharField(max_length=300, required=False, allow_blank=True)
    volume = serializers.CharField(max_length=50, required=False, allow_blank=True)
    issue = serializers.CharField(max_length=50, required=False, allow_blank=True)
    pages = serializers.CharField(max_length=50, required=False, allow_blank=True)
    keywords = serializers.ListField(
        child=serializers.CharField(max_length=100),
        required=False
    )
    source = serializers.CharField(max_length=50, required=False, allow_blank=True)
    source_id = serializers.CharField(max_length=100, required=False, allow_blank=True)
    citations_count = serializers.IntegerField(required=False, allow_null=True)


class SearchRequestSerializer(serializers.Serializer):
    """Serializer for search requests."""
    query = serializers.CharField(max_length=1000)
    max_results = serializers.IntegerField(default=50, min_value=1, max_value=1000)
    sources = serializers.ListField(
        child=serializers.ChoiceField(choices=[
            'arxiv', 'pubmed', 'ieee', 'acm', 'springer', 
            'elsevier', 'scihub', 'google_scholar', 'crossref', 'asm'
        ]),
        required=False,
        allow_empty=True
    )
    date_from = serializers.DateTimeField(required=False, allow_null=True)
    date_to = serializers.DateTimeField(required=False, allow_null=True)
    
    def validate(self, data):
        """Validate search request data."""
        if data.get('date_from') and data.get('date_to'):
            if data['date_from'] > data['date_to']:
                raise serializers.ValidationError(
                    "date_from cannot be after date_to"
                )
        return data


class SearchResultSerializer(serializers.Serializer):
    """Serializer for search results."""
    papers = PaperSerializer(many=True)
    total_found = serializers.IntegerField()
    search_time = serializers.FloatField()
    sources_searched = serializers.ListField(
        child=serializers.CharField(max_length=50)
    )
    errors = serializers.ListField(
        child=serializers.CharField(),
        required=False
    )


class DownloadTaskCreateSerializer(serializers.Serializer):
    """Serializer for creating download tasks."""
    papers = PaperSerializer(many=True)
    
    def validate_papers(self, value):
        """Validate papers list."""
        if not value:
            raise serializers.ValidationError("Papers list cannot be empty")
        
        if len(value) > 500:
            raise serializers.ValidationError("Cannot download more than 500 papers at once")
        
        return value


class DownloadTaskSerializer(serializers.Serializer):
    """Serializer for download task responses."""
    task_id = serializers.CharField()
    status = serializers.ChoiceField(choices=[
        'pending', 'searching', 'downloading', 'completed', 'failed'
    ])
    progress = serializers.IntegerField()
    downloaded_count = serializers.IntegerField()
    failed_count = serializers.IntegerField()
    total_papers = serializers.IntegerField()
    success_rate = serializers.FloatField(required=False)
    duration = serializers.FloatField(required=False, allow_null=True)
    created_at = serializers.DateTimeField()
    started_at = serializers.DateTimeField(required=False, allow_null=True)
    completed_at = serializers.DateTimeField(required=False, allow_null=True)
    error_message = serializers.CharField(required=False, allow_blank=True)


class SourceSerializer(serializers.Serializer):
    """Serializer for available sources."""
    id = serializers.CharField()
    name = serializers.CharField()
    description = serializers.CharField()
    enabled = serializers.BooleanField()


class DownloadProgressSerializer(serializers.Serializer):
    """Serializer for download progress updates."""
    task_id = serializers.CharField()
    status = serializers.CharField()
    progress = serializers.IntegerField()
    current_paper = serializers.CharField(required=False, allow_blank=True)
    message = serializers.CharField(required=False, allow_blank=True)
    downloaded_count = serializers.IntegerField()
    failed_count = serializers.IntegerField()
    total_count = serializers.IntegerField()


class ErrorResponseSerializer(serializers.Serializer):
    """Serializer for error responses."""
    error = serializers.CharField()
    message = serializers.CharField()
    details = serializers.DictField(required=False) 