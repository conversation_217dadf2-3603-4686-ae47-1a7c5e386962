"""
Presentation layer services for notifications and infrastructure.
"""

import async<PERSON>
import json
from typing import Dict, Any, Optional, List
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

from ..domain.interfaces import INotificationService, IDownloadTaskRepository
from ..domain.models import DownloadTask, Paper
from ..data.repositories import PaperRepository


class NotificationService(INotificationService):
    """WebSocket notification service implementation."""
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
    
    async def send_search_progress(self, query_id: str, progress: Dict[str, Any]) -> None:
        """Send search progress notification via WebSocket."""
        if self.channel_layer:
            await self.channel_layer.group_send(
                f"search_{query_id}",
                {
                    "type": "search_progress",
                    "progress": progress
                }
            )
    
    async def send_download_progress(self, task_id: str, progress: Dict[str, Any]) -> None:
        """Send download progress notification via WebSocket."""
        if self.channel_layer:
            await self.channel_layer.group_send(
                f"download_{task_id}",
                {
                    "type": "download_progress",
                    "progress": progress
                }
            )
    
    async def send_task_completed(self, task_id: str, result: Dict[str, Any]) -> None:
        """Send task completion notification via WebSocket."""
        if self.channel_layer:
            await self.channel_layer.group_send(
                f"download_{task_id}",
                {
                    "type": "task_completed",
                    "result": result
                }
            )


class DownloadTaskRepository(IDownloadTaskRepository):
    """Download task repository implementation using Django cache."""
    
    def __init__(self):
        from django.core.cache import cache
        self.cache = cache
        self.paper_repo = PaperRepository()
    
    def create_task(self, papers: List[Paper]) -> DownloadTask:
        """Create a new download task."""
        task = DownloadTask(papers=papers)
        
        # Store in cache
        self.cache.set(f"download_task_{task.task_id}", task, timeout=3600)
        
        return task
    
    def get_task(self, task_id: str) -> Optional[DownloadTask]:
        """Get download task by ID."""
        return self.cache.get(f"download_task_{task_id}")
    
    def update_task(self, task: DownloadTask) -> None:
        """Update download task."""
        self.cache.set(f"download_task_{task.task_id}", task, timeout=3600)
    
    def get_active_tasks(self) -> List[DownloadTask]:
        """Get all active download tasks."""
        # This is a simplified implementation
        # In production, you might use a database or different caching strategy
        return [] 