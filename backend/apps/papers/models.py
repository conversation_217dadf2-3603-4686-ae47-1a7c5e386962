"""
Django ORM models for the papers application.
These are used for database persistence.
"""

from django.db import models
from django.contrib.auth.models import User


class Paper(models.Model):
    """Database model for research papers."""
    
    title = models.Char<PERSON>ield(max_length=500)
    doi = models.CharField(max_length=200, blank=True, null=True, db_index=True)
    url = models.URLField(blank=True, null=True)
    pdf_url = models.URLField(blank=True, null=True)
    abstract = models.TextField(blank=True, null=True)
    publication_date = models.DateTimeField(blank=True, null=True)
    journal = models.CharField(max_length=300, blank=True, null=True)
    volume = models.CharField(max_length=50, blank=True, null=True)
    issue = models.CharField(max_length=50, blank=True, null=True)
    pages = models.Char<PERSON>ield(max_length=50, blank=True, null=True)
    source = models.CharField(max_length=50, blank=True, null=True)
    source_id = models.Char<PERSON><PERSON>(max_length=100, blank=True, null=True)
    citations_count = models.IntegerField(blank=True, null=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        unique_together = ['doi', 'source_id']
    
    def __str__(self):
        return self.title[:100]


class Author(models.Model):
    """Database model for paper authors."""
    
    name = models.CharField(max_length=200)
    affiliation = models.CharField(max_length=500, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    
    # Many-to-many relationship with papers
    papers = models.ManyToManyField(Paper, related_name='authors')
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['name']
    
    def __str__(self):
        return self.name


class SearchQuery(models.Model):
    """Database model for search queries (for analytics)."""
    
    query = models.CharField(max_length=1000)
    max_results = models.IntegerField(default=50)
    sources = models.JSONField(default=list)
    results_found = models.IntegerField(default=0)
    search_time = models.FloatField(default=0.0)
    
    # User tracking (optional)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = "Search queries"
    
    def __str__(self):
        return f"'{self.query}' - {self.results_found} results"


class DownloadTask(models.Model):
    """Database model for download tasks."""
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('searching', 'Searching'),
        ('downloading', 'Downloading'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    task_id = models.CharField(max_length=100, unique=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    progress = models.IntegerField(default=0)
    total_papers = models.IntegerField(default=0)
    downloaded_papers = models.IntegerField(default=0)
    failed_downloads = models.IntegerField(default=0)
    error_message = models.TextField(blank=True, null=True)
    
    # User tracking
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Task {self.task_id} - {self.status}"
    
    @property
    def success_rate(self):
        """Calculate success rate percentage."""
        total_processed = self.downloaded_papers + self.failed_downloads
        if total_processed == 0:
            return 0.0
        return (self.downloaded_papers / total_processed) * 100 