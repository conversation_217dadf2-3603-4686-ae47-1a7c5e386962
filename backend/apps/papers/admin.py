"""
Django admin configuration for the papers application.
"""

from django.contrib import admin
from .models import Paper, Author, SearchQuery, DownloadTask


@admin.register(Paper)
class PaperAdmin(admin.ModelAdmin):
    """Admin configuration for Paper model."""
    
    list_display = ['title', 'doi', 'source', 'publication_date', 'created_at']
    list_filter = ['source', 'publication_date', 'created_at']
    search_fields = ['title', 'doi', 'abstract']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'doi', 'url', 'pdf_url')
        }),
        ('Content', {
            'fields': ('abstract',)
        }),
        ('Publication Details', {
            'fields': ('publication_date', 'journal', 'volume', 'issue', 'pages')
        }),
        ('Source Information', {
            'fields': ('source', 'source_id', 'citations_count')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Author)
class AuthorAdmin(admin.ModelAdmin):
    """Admin configuration for Author model."""
    
    list_display = ['name', 'affiliation', 'email', 'created_at']
    search_fields = ['name', 'affiliation', 'email']
    readonly_fields = ['created_at']
    filter_horizontal = ['papers']


@admin.register(SearchQuery)
class SearchQueryAdmin(admin.ModelAdmin):
    """Admin configuration for SearchQuery model."""
    
    list_display = ['query', 'results_found', 'search_time', 'user', 'created_at']
    list_filter = ['created_at', 'user']
    search_fields = ['query']
    readonly_fields = ['created_at']
    
    fieldsets = (
        ('Query Information', {
            'fields': ('query', 'max_results', 'sources')
        }),
        ('Results', {
            'fields': ('results_found', 'search_time')
        }),
        ('User Information', {
            'fields': ('user', 'ip_address')
        }),
        ('Timestamp', {
            'fields': ('created_at',)
        }),
    )


@admin.register(DownloadTask)
class DownloadTaskAdmin(admin.ModelAdmin):
    """Admin configuration for DownloadTask model."""
    
    list_display = ['task_id', 'status', 'progress', 'total_papers', 'downloaded_papers', 'failed_downloads', 'created_at']
    list_filter = ['status', 'created_at', 'completed_at']
    search_fields = ['task_id']
    readonly_fields = ['task_id', 'created_at', 'started_at', 'completed_at', 'success_rate']
    
    fieldsets = (
        ('Task Information', {
            'fields': ('task_id', 'status', 'progress')
        }),
        ('Progress Details', {
            'fields': ('total_papers', 'downloaded_papers', 'failed_downloads', 'success_rate')
        }),
        ('Error Information', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
        ('User Information', {
            'fields': ('user', 'ip_address')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'started_at', 'completed_at')
        }),
    )
    
    def success_rate(self, obj):
        """Display success rate as percentage."""
        return f"{obj.success_rate:.1f}%"
    success_rate.short_description = 'Success Rate' 