"""
Celery tasks for background processing.
"""

from celery import shared_task
import asyncio
import traceback
from .presentation.services import get_paper_repository, get_task_repository, get_notification_service
from .domain.usecases import BulkDownloadUseCase


@shared_task
def execute_download_task(task_id: str):
    """
    Execute a download task in the background.
    
    Args:
        task_id: ID of the download task to execute
    """
    try:
        print(f"Starting download task: {task_id}")
        
        # Initialize repositories and services
        paper_repo = get_paper_repository()
        task_repo = get_task_repository()
        notification_service = get_notification_service()
        
        # Create use case
        download_use_case = BulkDownloadUseCase(
            paper_repository=paper_repo,
            task_repository=task_repo,
            notification_service=notification_service
        )
        
        # Execute task asynchronously
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            print(f"Executing download task: {task_id}")
            task = loop.run_until_complete(
                download_use_case.execute_download_task(task_id)
            )
            
            result = {
                'task_id': task_id,
                'status': task.status,
                'downloaded_count': getattr(task, 'downloaded_count', 0),
                'failed_count': getattr(task, 'failed_count', 0),
                'total_papers': len(task.papers)
            }
            
            print(f"Download task completed: {result}")
            return result
            
        finally:
            loop.close()
            
    except Exception as e:
        print(f"Error in download task {task_id}: {str(e)}")
        traceback.print_exc()
        
        # Update task status to failed
        try:
            task_repo = get_task_repository()
            task = task_repo.get_task(task_id)
            if task:
                task.status = 'failed'
                task.error_message = str(e)
                task_repo.update_task(task)
        except Exception as update_error:
            print(f"Failed to update task status: {update_error}")
        
        return {
            'task_id': task_id,
            'status': 'failed',
            'error': str(e)
        } 