#!/usr/bin/env python3
"""
Simple test for improved ASM search with easier queries.
"""

import asyncio
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from apps.papers.data.services import ASMSearchService
from apps.papers.data.models import SearchRequestDTO


async def test_simple_queries():
    """Test ASM search with simpler queries to verify it works."""
    
    print("🧪 Testing Improved ASM Search with Simple Queries")
    print("=" * 60)
    
    service = ASMSearchService()
    async with service:
        
        # Test 1: Simple keyword search
        print("\n🔍 Test 1: Simple keyword search...")
        request1 = SearchRequestDTO(query="Streptomyces", max_results=5)
        papers1 = await service.search(request1)
        
        print(f"Found {len(papers1)} papers for 'Streptomyces'")
        for i, paper in enumerate(papers1, 1):
            print(f"  {i}. {paper.title[:50]}...")
            print(f"     DOI: {paper.doi}")
            print(f"     Journal: {paper.journal}")
        
        # Test 2: Search for biosynthetic
        print("\n🔍 Test 2: Biosynthetic search...")
        request2 = SearchRequestDTO(query="biosynthetic", max_results=5)
        papers2 = await service.search(request2)
        
        print(f"Found {len(papers2)} papers for 'biosynthetic'")
        for i, paper in enumerate(papers2, 1):
            print(f"  {i}. {paper.title[:50]}...")
            print(f"     DOI: {paper.doi}")
            print(f"     Journal: {paper.journal}")
        
        # Test 3: Search for comparative genomics
        print("\n🔍 Test 3: Comparative genomics search...")
        request3 = SearchRequestDTO(query="comparative genomics", max_results=5)
        papers3 = await service.search(request3)
        
        print(f"Found {len(papers3)} papers for 'comparative genomics'")
        for i, paper in enumerate(papers3, 1):
            print(f"  {i}. {paper.title[:50]}...")
            print(f"     DOI: {paper.doi}")
            print(f"     Journal: {paper.journal}")
        
        # Test 4: Search for the specific DOI
        print("\n🔍 Test 4: DOI search...")
        request4 = SearchRequestDTO(query="10.1128/msystems.00489-21", max_results=5)
        papers4 = await service.search(request4)
        
        print(f"Found {len(papers4)} papers for DOI search")
        target_found = False
        for i, paper in enumerate(papers4, 1):
            print(f"  {i}. {paper.title[:50]}...")
            print(f"     DOI: {paper.doi}")
            print(f"     Journal: {paper.journal}")
            if paper.doi == "10.1128/msystems.00489-21":
                target_found = True
                print("     🎯 TARGET PAPER FOUND!")
        
        print(f"\n✅ Summary:")
        print(f"  Target paper found via DOI search: {'✅' if target_found else '❌'}")
        print(f"  Total papers across all tests: {len(papers1) + len(papers2) + len(papers3) + len(papers4)}")

if __name__ == "__main__":
    asyncio.run(test_simple_queries()) 