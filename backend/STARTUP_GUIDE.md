# 🚀 Paper Downloader - Complete Startup Guide

## 📋 Prerequisites
- Python 3.13+ installed
- Node.js 18+ installed  
- Git installed

## 🏃‍♂️ Quick Start (Both Servers)

### Option 1: Automated Startup Script
```bash
# Navigate to project root
cd /Users/<USER>/Downloads/Codes/paper-downloader

# Backend (Terminal 1)
cd backend
source venv/bin/activate
python manage.py runserver

# Frontend (Terminal 2) 
cd ../frontend
npm run dev
```

### Option 2: Individual Server Management

#### Backend Server (Django)
```bash
# Terminal 1 - Backend
cd /Users/<USER>/Downloads/Codes/paper-downloader/backend
source venv/bin/activate
python manage.py runserver
```

#### Frontend Server (Next.js) 
```bash
# Terminal 2 - Frontend  
cd /Users/<USER>/Downloads/Codes/paper-downloader/frontend
npm run dev
```

## 🌐 Access Points

| Service | URL | Description |
|---------|-----|-------------|
| **Frontend** | http://localhost:3000 | Main user interface |
| **Backend API** | http://localhost:8000/api | REST API endpoints |
| **Django Admin** | http://localhost:8000/admin | Admin interface |
| **API Documentation** | http://localhost:8000/api/v1/docs | Interactive API docs |

## 🧪 Testing the Application

### 1. **Search Functionality**
- Navigate to http://localhost:3000
- Enter a research topic (e.g., "machine learning")
- Click "Search Papers"
- View results from multiple sources

### 2. **Download Papers**
- Select papers from search results
- Click "Download Selected" 
- Monitor download progress in real-time
- Check downloaded files in `/backend/downloads/`

### 3. **API Testing**
```bash
# Test search endpoint
curl "http://localhost:8000/api/v1/search/" \
  -H "Content-Type: application/json" \
  -d '{"query": "artificial intelligence", "max_results": 10}'

# Test available sources
curl "http://localhost:8000/api/v1/sources/"
```

## 📊 Available Paper Sources

| Source | Status | Description |
|--------|--------|-------------|
| **arXiv** | ✅ Active | Open access preprints |
| **Crossref** | ✅ Active | Academic publications metadata |
| **PubMed** | ✅ Active | Biomedical literature |
| **Google Scholar** | ⚠️ Limited | Requires Playwright (disabled) |
| **IEEE Xplore** | ⚠️ Limited | Requires Playwright (disabled) |
| **Sci-Hub** | ⚠️ Limited | PDF downloads (use responsibly) |

## 🔧 Development Commands

### Backend (Django)
```bash
# Always activate venv first!
source venv/bin/activate

# Create migrations
python manage.py makemigrations

# Apply migrations  
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Run tests
python manage.py test

# Install new packages
pip install package_name
pip freeze > requirements.txt
```

### Frontend (Next.js)
```bash
# Development server
npm run dev

# Production build
npm run build
npm start

# Type checking
npm run type-check

# Install new packages
npm install package_name
```

## 🐛 Troubleshooting

### Backend Issues

#### **"ModuleNotFoundError"**
```bash
# Ensure virtual environment is activated
source venv/bin/activate
pip install -r requirements.txt
```

#### **"Database Error"**
```bash
# Reset database
rm db.sqlite3
python manage.py migrate
```

#### **"Port 8000 already in use"**
```bash
# Kill existing process
lsof -ti:8000 | xargs kill -9
# Or use different port
python manage.py runserver 8001
```

### Frontend Issues

#### **"Module not found"**
```bash
# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

#### **"Port 3000 already in use"**
```bash
# Kill existing process  
lsof -ti:3000 | xargs kill -9
# Or use different port
npm run dev -- -p 3001
```

### Common Issues

#### **CORS Errors**
- Backend includes `django-cors-headers` configured for `localhost:3000`
- Check browser console for specific CORS messages

#### **WebSocket Connection Issues**
- Ensure Redis is running for real-time features
- Install Redis: `brew install redis` (macOS)

## 🔄 Memory Rules for AI Assistant

1. **Always activate venv**: `source venv/bin/activate` before any Python commands
2. **Project structure**: Backend in `/backend/`, Frontend in `/frontend/`
3. **MVVM compliance**: All changes must follow strict MVVM architecture
4. **Virtual environment path**: `/Users/<USER>/Downloads/Codes/paper-downloader/backend/venv/`

## 📁 Key File Locations

```
paper-downloader/
├── backend/
│   ├── venv/                    # Python virtual environment
│   ├── apps/papers/             # Main Django app (MVVM structure)
│   ├── db.sqlite3              # SQLite database
│   ├── requirements.txt        # Python dependencies
│   └── manage.py               # Django management script
├── frontend/
│   ├── app/                    # Next.js app directory
│   ├── components/             # React components (MVVM Views)
│   ├── lib/                    # Services & hooks (MVVM ViewModels)
│   ├── package.json            # Node.js dependencies
│   └── next.config.js          # Next.js configuration
└── README.md                   # Project documentation
```

## 🎯 Next Steps

1. **Enable Playwright** (optional):
   ```bash
   pip install playwright==1.47.0
   playwright install
   ```

2. **Setup Redis** (for real-time features):
   ```bash
   brew install redis
   redis-server
   ```

3. **Configure Environment Variables**:
   - Copy `.env.example` to `.env.local` (frontend)
   - Add API keys for external services

4. **Production Deployment**:
   - Configure production database
   - Set up reverse proxy (Nginx)
   - Enable HTTPS/SSL certificates

---

## 📞 Support

For issues or questions:
- Check Django logs: Terminal running `python manage.py runserver`
- Check Next.js logs: Terminal running `npm run dev`  
- Review browser console for frontend errors
- Use Django admin panel for backend data inspection 