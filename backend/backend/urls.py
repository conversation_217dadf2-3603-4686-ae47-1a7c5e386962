"""
URL configuration for backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt

# Simple view to handle development tool requests
@csrf_exempt
def dev_tool_handler(request):
    """Handle development tool requests gracefully"""
    return JsonResponse({'status': 'ok', 'message': 'Development endpoint'})

# Root API status endpoint
def api_root(request):
    """Root API endpoint providing status information"""
    return JsonResponse({
        'status': 'ok',
        'message': 'Paper Downloader API',
        'version': '1.0.0',
        'endpoints': {
            'health': '/api/v1/health/',
            'search': '/api/v1/papers/search/',
            'sources': '/api/v1/papers/sources/',
            'download': '/api/v1/papers/download/',
        }
    })

urlpatterns = [
    # Root endpoint
    path('', api_root, name='api_root'),
    
    # Admin
    path('admin/', admin.site.urls),
    
    # API endpoints
    path('api/v1/', include('apps.papers.urls')),
    
    # Handle development tool requests
    path('current-url/', dev_tool_handler, name='current_url'),
    path('.identity/', dev_tool_handler, name='identity'),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT) 