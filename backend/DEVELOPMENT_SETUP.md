# Development Setup Guide

## 🐍 Python Virtual Environment

**⚠️ IMPORTANT: Always activate the virtual environment before working with the backend!**

### Virtual Environment Location
```
/Users/<USER>/Downloads/Codes/paper-downloader/backend/venv/
```

### Required Commands Before Any Backend Work

1. **Navigate to backend directory:**
   ```bash
   cd /Users/<USER>/Downloads/Codes/paper-downloader/backend
   ```

2. **Activate virtual environment:**
   ```bash
   source venv/bin/activate
   ```

3. **Verify activation (you should see `(venv)` in your prompt):**
   ```bash
   which python
   # Should show: /Users/<USER>/Downloads/Codes/paper-downloader/backend/venv/bin/python
   ```

### Why This Is Required

- Avoids "externally-managed-environment" errors
- Ensures proper dependency isolation
- Prevents conflicts with system Python packages
- Required for pip installations and Django commands

### Development Workflow

```bash
# 1. Always start with activation
cd /Users/<USER>/Downloads/Codes/paper-downloader/backend
source venv/bin/activate

# 2. Install dependencies
pip install -r requirements.txt

# 3. Run Django commands
python manage.py migrate
python manage.py runserver

# 4. When done, deactivate (optional)
deactivate
```

### Quick Commands Reference

| Command | Purpose |
|---------|---------|
| `source venv/bin/activate` | Activate virtual environment |
| `deactivate` | Deactivate virtual environment |
| `which python` | Check if venv is active |
| `pip list` | Show installed packages in venv |

### 🔄 Memory Rule for AI Assistant

**FOR AI ASSISTANT: Before running ANY Python/Django commands in the backend directory, ALWAYS run `source venv/bin/activate` first. This is mandatory for all pip installs, Django management commands, and Python script execution.**

### ✅ Application Status

**Backend (Django)**: ✅ Running on http://localhost:8000  
**Frontend (Next.js)**: ✅ Running on http://localhost:3000  
**Database**: ✅ SQLite configured and migrated  
**Virtual Environment**: ✅ Created and activated

---

## 🚀 Complete Application Startup

### Terminal 1 - Backend (Django)
```bash
cd /Users/<USER>/Downloads/Codes/paper-downloader/backend
source venv/bin/activate
pip install -r requirements.txt
python manage.py migrate
python manage.py runserver
```

### Terminal 2 - Frontend (Next.js)
```bash
cd /Users/<USER>/Downloads/Codes/paper-downloader/frontend
npm run dev
```

### Access Points
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000/api
- **Django Admin**: http://localhost:8000/admin 