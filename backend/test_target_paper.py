#!/usr/bin/env python3
"""
Focused test to find the target Streptomyces paper using different approaches.
"""

import asyncio
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from apps.papers.data.services import ASMSearchService
from apps.papers.data.models import SearchRequestDTO


async def test_target_paper_search():
    """Test different approaches to find the target paper."""
    
    print("🎯 Focused Search for Target Streptomyces Paper")
    print("=" * 60)
    
    target_doi = "10.1128/msystems.00489-21"
    target_title_keywords = ["Streptomyces", "biosynthetic", "potential", "rugose", "ornamented", "spores"]
    
    service = ASMSearchService()
    async with service:
        
        # Test 1: Shortened title search
        print("\n🔍 Test 1: Shortened title search...")
        request1 = SearchRequestDTO(query="Comparative Genomics Remarkable Biosynthetic Potential Streptomyces", max_results=10)
        papers1 = await service.search(request1)
        
        print(f"Found {len(papers1)} papers")
        target_found_1 = False
        for i, paper in enumerate(papers1, 1):
            print(f"  {i}. {paper.title[:60]}...")
            print(f"     DOI: {paper.doi}")
            if paper.doi == target_doi:
                target_found_1 = True
                print("     🎯 TARGET FOUND!")
        
        # Test 2: Keywords search
        print("\n🔍 Test 2: Keywords search...")
        request2 = SearchRequestDTO(query="Streptomyces biosynthetic rugose spores", max_results=10)
        papers2 = await service.search(request2)
        
        print(f"Found {len(papers2)} papers")
        target_found_2 = False
        for i, paper in enumerate(papers2, 1):
            print(f"  {i}. {paper.title[:60]}...")
            print(f"     DOI: {paper.doi}")
            if paper.doi == target_doi:
                target_found_2 = True
                print("     🎯 TARGET FOUND!")
        
        # Test 3: DOI-based search
        print("\n🔍 Test 3: DOI-based search...")
        request3 = SearchRequestDTO(query="10.1128/msystems.00489-21", max_results=5)
        papers3 = await service.search(request3)
        
        print(f"Found {len(papers3)} papers")
        target_found_3 = False
        for i, paper in enumerate(papers3, 1):
            print(f"  {i}. {paper.title[:60]}...")
            print(f"     DOI: {paper.doi}")
            if paper.doi == target_doi:
                target_found_3 = True
                print("     🎯 TARGET FOUND!")
        
        # Test 4: Just "biosynthetic potential"
        print("\n🔍 Test 4: Biosynthetic potential search...")
        request4 = SearchRequestDTO(query="biosynthetic potential", max_results=10)
        papers4 = await service.search(request4)
        
        print(f"Found {len(papers4)} papers")
        target_found_4 = False
        for i, paper in enumerate(papers4, 1):
            print(f"  {i}. {paper.title[:60]}...")
            print(f"     DOI: {paper.doi}")
            if paper.doi == target_doi:
                target_found_4 = True
                print("     🎯 TARGET FOUND!")
        
        print(f"\n" + "=" * 60)
        print("🎯 TARGET PAPER SEARCH RESULTS:")
        print(f"  Shortened title search: {'✅' if target_found_1 else '❌'}")
        print(f"  Keywords search: {'✅' if target_found_2 else '❌'}")
        print(f"  DOI search: {'✅' if target_found_3 else '❌'}")
        print(f"  Biosynthetic potential: {'✅' if target_found_4 else '❌'}")
        print(f"  Any method found target: {'✅' if any([target_found_1, target_found_2, target_found_3, target_found_4]) else '❌'}")
        print("=" * 60)

if __name__ == "__main__":
    asyncio.run(test_target_paper_search()) 