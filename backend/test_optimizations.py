#!/usr/bin/env python3
"""
Test script to validate payload optimizations.
"""

import asyncio
import aiohttp
import sys
import time
from apps.papers.data.services import ArxivSearchService, CrossrefSearchService
from apps.papers.data.models import SearchRequestDTO, PaperSource

async def test_arxiv_optimization():
    """Test arXiv payload optimizations."""
    print("=" * 60)
    print("TESTING ARXIV PAYLOAD OPTIMIZATIONS")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        service = ArxivSearchService(session)
        
        # Test with small query
        request = SearchRequestDTO(
            query='machine learning',
            max_results=5,
            sources=[PaperSource.ARXIV]
        )
        
        start_time = time.time()
        papers = await service.search(request)
        end_time = time.time()
        
        print(f"\nResults:")
        print(f"  Papers returned: {len(papers)}")
        print(f"  Search time: {end_time - start_time:.2f} seconds")
        
        if papers:
            paper = papers[0]
            print(f"\nSample paper analysis:")
            print(f"  Title: {paper.title[:80]}...")
            print(f"  Authors: <AUTHORS>
            print(f"  Abstract length: {len(paper.abstract or '')} chars")
            print(f"  Keywords: {len(paper.keywords)} keywords")
            print(f"  First few authors: {[a.name for a in paper.authors[:3]]}")
            print(f"  First few keywords: {paper.keywords[:3]}")
            
            # Check if optimizations are working
            abstract_len = len(paper.abstract or '')
            if abstract_len <= 400:
                print(f"  ✅ Abstract truncation working (≤400 chars)")
            else:
                print(f"  ❌ Abstract truncation not working ({abstract_len} chars)")
                
            if len(paper.authors) <= 10:
                print(f"  ✅ Author limiting working (≤10 authors)")
            else:
                print(f"  ❌ Author limiting not working ({len(paper.authors)} authors)")
                
            if len(paper.keywords) <= 5:
                print(f"  ✅ Keyword limiting working (≤5 keywords)")
            else:
                print(f"  ❌ Keyword limiting not working ({len(paper.keywords)} keywords)")

async def test_crossref_optimization():
    """Test Crossref payload optimizations."""
    print("\n" + "=" * 60)
    print("TESTING CROSSREF PAYLOAD OPTIMIZATIONS")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        service = CrossrefSearchService(session)
        
        # Test with small query
        request = SearchRequestDTO(
            query='machine learning',
            max_results=5,
            sources=[PaperSource.CROSSREF]
        )
        
        start_time = time.time()
        papers = await service.search(request)
        end_time = time.time()
        
        print(f"\nResults:")
        print(f"  Papers returned: {len(papers)}")
        print(f"  Search time: {end_time - start_time:.2f} seconds")
        
        if papers:
            paper = papers[0]
            print(f"\nSample paper analysis:")
            print(f"  Title: {paper.title[:80]}...")
            print(f"  Authors: <AUTHORS>
            print(f"  Abstract length: {len(paper.abstract or '')} chars")
            print(f"  Keywords: {len(paper.keywords)} keywords")
            print(f"  DOI: {paper.doi}")
            print(f"  Journal: {paper.journal}")
            
            # Check if optimizations are working
            abstract_len = len(paper.abstract or '')
            if abstract_len <= 400:
                print(f"  ✅ Abstract truncation working (≤400 chars)")
            else:
                print(f"  ❌ Abstract truncation not working ({abstract_len} chars)")
                
            if len(paper.authors) <= 10:
                print(f"  ✅ Author limiting working (≤10 authors)")
            else:
                print(f"  ❌ Author limiting not working ({len(paper.authors)} authors)")
                
            if len(paper.keywords) <= 5:
                print(f"  ✅ Keyword limiting working (≤5 keywords)")
            else:
                print(f"  ❌ Keyword limiting not working ({len(paper.keywords)} keywords)")

async def main():
    """Run all optimization tests."""
    print("PAYLOAD OPTIMIZATION VALIDATION TEST")
    print("This test validates that our optimizations are working correctly.")
    print("It checks field limiting, truncation, and performance improvements.")
    
    try:
        await test_arxiv_optimization()
        await test_crossref_optimization()
        
        print("\n" + "=" * 60)
        print("OPTIMIZATION TEST COMPLETE")
        print("=" * 60)
        print("✅ All tests completed successfully!")
        print("Check the output above to verify optimizations are working.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
