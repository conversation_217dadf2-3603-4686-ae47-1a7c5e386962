#!/usr/bin/env python3
"""
Test script to compare current ASM search results with improved approach
that mimics the ASM website's search parameters.
"""

import asyncio
import aiohttp
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from apps.papers.data.services import ASMSearchService
from apps.papers.data.models import SearchRequestDTO


async def test_current_vs_improved():
    """Compare current ASM search with improved Crossref filtering approach."""
    
    print("=" * 80)
    print("ASM Search Comparison: Current vs Improved Approach")
    print("=" * 80)
    
    query = "Comparative Genomics Reveals a Remarkable Biosynthetic Potential of the Streptomyces Phylogenetic Lineage Associated with Rugose-Ornamented Spores"
    
    # Test 1: Current implementation
    print("\n🔍 Testing Current ASM Search Implementation...")
    service = ASMSearchService()
    async with service:
        request = SearchRequestDTO(query=query, max_results=5)
        current_papers = await service.search(request)
        
    print(f"Current approach found: {len(current_papers)} papers")
    for i, paper in enumerate(current_papers, 1):
        print(f"  {i}. {paper.title[:60]}...")
        print(f"     DOI: {paper.doi}")
        print(f"     Journal: {paper.journal}")
        print(f"     Source: {paper.source}")
        print()
    
    # Test 2: Improved Crossref approach with container-title filter
    print("🔍 Testing Improved Crossref Approach with Journal Filtering...")
    
    async with aiohttp.ClientSession() as session:
        # Test different ASM journals
        asm_journals = ["mSystems", "mBio", "mSphere", "Applied and Environmental Microbiology"]
        improved_papers = []
        
        for journal in asm_journals:
            url = "https://api.crossref.org/works"
            params = {
                'query': query,
                'filter': f'container-title:{journal}',
                'rows': 5,
                'mailto': '<EMAIL>'
            }
            
            print(f"\n   Searching in {journal}...")
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    items = data.get('message', {}).get('items', [])
                    
                    for item in items:
                        title = item.get('title', [''])[0] if item.get('title') else ''
                        doi = item.get('DOI', '')
                        container_title = item.get('container-title', [])
                        journal_name = container_title[0] if container_title else 'Unknown'
                        
                        # Clean HTML from title
                        import re
                        clean_title = re.sub(r'<[^>]+>', '', title)
                        clean_title = re.sub(r'\s+', ' ', clean_title).strip()
                        
                        improved_papers.append({
                            'title': clean_title,
                            'doi': doi,
                            'journal': journal_name,
                            'source': 'Crossref-Filtered'
                        })
                        
                    print(f"     Found {len(items)} papers in {journal}")
                else:
                    print(f"     Failed to search {journal} (status: {response.status})")
    
    print(f"\nImproved approach found: {len(improved_papers)} papers")
    for i, paper in enumerate(improved_papers, 1):
        print(f"  {i}. {paper['title'][:60]}...")
        print(f"     DOI: {paper['doi']}")
        print(f"     Journal: {paper['journal']}")
        print(f"     Source: {paper['source']}")
        print()
    
    # Test 3: Direct comparison
    print("=" * 80)
    print("COMPARISON ANALYSIS")
    print("=" * 80)
    
    target_doi = "10.1128/msystems.00489-21"
    
    # Check if target paper found in current approach
    current_has_target = any(p.doi == target_doi for p in current_papers)
    
    # Check if target paper found in improved approach  
    improved_has_target = any(p['doi'] == target_doi for p in improved_papers)
    
    print(f"Target Paper (DOI: {target_doi}):")
    print(f"  Found by current approach: {'✅' if current_has_target else '❌'}")
    print(f"  Found by improved approach: {'✅' if improved_has_target else '❌'}")
    
    print(f"\nResult Counts:")
    print(f"  Current approach: {len(current_papers)} papers")
    print(f"  Improved approach: {len(improved_papers)} papers")
    
    if improved_has_target and not current_has_target:
        print(f"\n🎯 IMPROVEMENT OPPORTUNITY:")
        print(f"  The improved approach finds the target paper that current approach misses!")
        print(f"  We should implement Crossref container-title filtering.")
    elif current_has_target and not improved_has_target:
        print(f"\n⚠️  CURRENT APPROACH ADVANTAGE:")
        print(f"  Current approach finds target paper, improved approach doesn't.")
        print(f"  The PubMed fallback is working well.")
    elif both_find_target := (current_has_target and improved_has_target):
        print(f"\n✅ BOTH APPROACHES WORK:")
        print(f"  Both find the target paper, but results may differ in completeness.")
    else:
        print(f"\n❌ NEITHER FINDS TARGET:")
        print(f"  Neither approach finds the target paper - investigation needed.")
    
    print("=" * 80)


if __name__ == "__main__":
    asyncio.run(test_current_vs_improved()) 