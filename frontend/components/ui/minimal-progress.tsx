'use client'

import { useState } from 'react'
import { Download, ChevronUp, ChevronDown } from 'lucide-react'
import { Button } from './button'
import { Progress } from './progress'
import { Card, CardContent } from './card'

interface ActiveDownload {
  id: string
  type: 'bulk' | 'single'
  status: string
  progress: number
  title?: string
  paper_id?: string
  total_papers?: number
  completed_papers?: number
  failed_papers?: number
  created_at: string
}

interface MinimalProgressProps {
  totalActiveDownloads: number
  globalProgress: number
  activeDownloads: ActiveDownload[]
  onShowDetails?: () => void
}

export function MinimalProgress({ 
  totalActiveDownloads, 
  globalProgress, 
  activeDownloads,
  onShowDetails 
}: MinimalProgressProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  if (totalActiveDownloads === 0) return null

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Card className="w-80 shadow-lg border-2">
        <CardContent className="p-3">
          {/* Minimal view */}
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 flex-1">
              <Download className="w-4 h-4 text-blue-500" />
              <div className="flex-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">
                    {totalActiveDownloads} downloads
                  </span>
                  <span className="text-muted-foreground">
                    {Math.round(globalProgress)}%
                  </span>
                </div>
                <Progress 
                  value={globalProgress} 
                  className="h-2 mt-1" 
                />
              </div>
            </div>
            
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="h-6 w-6 p-0"
              >
                {isExpanded ? 
                  <ChevronDown className="w-3 h-3" /> : 
                  <ChevronUp className="w-3 h-3" />
                }
              </Button>
              {onShowDetails && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onShowDetails}
                  className="h-6 px-2 text-xs"
                >
                  View All
                </Button>
              )}
            </div>
          </div>

          {/* Expanded view */}
          {isExpanded && (
            <div className="mt-3 pt-3 border-t max-h-40 overflow-y-auto">
              <div className="space-y-2">
                {activeDownloads.slice(0, 5).map((download) => (
                  <div key={download.id} className="flex items-center gap-2">
                    <div className="flex-1 min-w-0">
                      <div className="text-xs font-medium truncate">
                        {download.type === 'single' 
                          ? download.title 
                          : `Bulk Download (${download.total_papers} papers)`
                        }
                      </div>
                      <Progress 
                        value={download.progress} 
                        className="h-1 mt-1" 
                      />
                    </div>
                    <span className="text-xs text-muted-foreground tabular-nums">
                      {Math.round(download.progress)}%
                    </span>
                  </div>
                ))}
                {activeDownloads.length > 5 && (
                  <div className="text-xs text-muted-foreground text-center pt-1">
                    +{activeDownloads.length - 5} more downloads
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
