'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Search, History, X } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { SearchHistoryDropdown } from './search-history-dropdown'
import { SearchHistoryItem } from '@/lib/utils/storage'
import { PaperSource } from '@/lib/types'

interface EnhancedSearchInputProps {
  value: string
  onChange: (value: string) => void
  onSubmit: () => void
  placeholder?: string
  disabled?: boolean
  searchHistory: SearchHistoryItem[]
  onSelectFromHistory: (query: string, sources: PaperSource[]) => void
  onRemoveFromHistory: (searchId: string) => void
  onClearHistory: () => void
  className?: string
  autoFocus?: boolean
}

export function EnhancedSearchInput({
  value,
  onChange,
  onSubmit,
  placeholder = "Enter your search query (e.g., 'machine learning', 'quantum computing')",
  disabled = false,
  searchHistory,
  onSelectFromHistory,
  onRemoveFromHistory,
  onClearHistory,
  className,
  autoFocus = true,
}: EnhancedSearchInputProps) {
  const [isHistoryOpen, setIsHistoryOpen] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Auto focus on mount
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      // Small delay to ensure the component is fully mounted
      const timer = setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [autoFocus])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Focus search input with Ctrl/Cmd + K
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault()
        inputRef.current?.focus()
      }
      
      // Close history dropdown with Escape
      if (event.key === 'Escape' && isHistoryOpen) {
        setIsHistoryOpen(false)
        inputRef.current?.focus()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isHistoryOpen])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    onChange(newValue)

    // Auto-open history when user starts typing and field was empty
    if (!value && newValue && searchHistory.length > 0) {
      setIsHistoryOpen(true)
    }

    // Close history when input becomes empty (user cleared it)
    if (value && !newValue) {
      setIsHistoryOpen(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      onSubmit()
      setIsHistoryOpen(false)
    } else if (e.key === 'ArrowDown' && searchHistory.length > 0) {
      e.preventDefault()
      setIsHistoryOpen(true)
    } else if (e.key === 'ArrowUp' && searchHistory.length > 0 && !value) {
      // Open history when pressing up arrow on empty field
      e.preventDefault()
      setIsHistoryOpen(true)
    }
  }

  const handleFocus = () => {
    setIsFocused(true)
    // Always open history when focusing if there's any history
    if (searchHistory.length > 0) {
      setIsHistoryOpen(true)
    }
  }

  const handleBlur = () => {
    setIsFocused(false)
    // Don't close immediately to allow clicking on history items
    setTimeout(() => {
      if (!containerRef.current?.contains(document.activeElement)) {
        setIsHistoryOpen(false)
      }
    }, 150)
  }

  const handleHistorySelect = (query: string, sources: PaperSource[]) => {
    onChange(query)
    onSelectFromHistory(query, sources)
    setIsHistoryOpen(false)
    inputRef.current?.focus()
  }

  const handleClearInput = () => {
    onChange('')
    inputRef.current?.focus()
  }

  const handleHistoryButtonClick = () => {
    setIsHistoryOpen(!isHistoryOpen)
    inputRef.current?.focus()
  }

  return (
    <div ref={containerRef} className={cn('relative w-full', className)}>
      <div className="relative">
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10">
          <Search className="w-5 h-5 text-muted-foreground" />
        </div>
        
        <Input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          autoComplete="off"
          autoCorrect="off"
          autoCapitalize="off"
          spellCheck="false"
          className={cn(
            'pl-10 pr-20 text-lg h-12 transition-all duration-200',
            isFocused && 'ring-2 ring-primary/20 border-primary',
            'focus:ring-2 focus:ring-primary/20 focus:border-primary'
          )}
        />

        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
          {value && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleClearInput}
              className="h-8 w-8 p-0 hover:bg-muted"
              tabIndex={-1}
            >
              <X className="w-4 h-4" />
            </Button>
          )}
          
          {searchHistory.length > 0 && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleHistoryButtonClick}
              className={cn(
                'h-8 w-8 p-0 hover:bg-muted transition-colors',
                isHistoryOpen && 'bg-muted'
              )}
              tabIndex={-1}
            >
              <History className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Keyboard shortcut hint */}
      {!isFocused && !value && (
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
          <div className="hidden md:flex items-center gap-1 text-xs text-muted-foreground">
            {searchHistory.length > 0 ? (
              <>
                <span className="text-xs">Focus to see history</span>
                <span className="mx-1">•</span>
              </>
            ) : null}
            <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded border">
              {navigator.platform.includes('Mac') ? '⌘' : 'Ctrl'}
            </kbd>
            <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded border">K</kbd>
          </div>
        </div>
      )}

      {/* Search History Dropdown */}
      <SearchHistoryDropdown
        searchHistory={searchHistory}
        isOpen={isHistoryOpen}
        onClose={() => setIsHistoryOpen(false)}
        onSelectSearch={handleHistorySelect}
        onRemoveSearch={onRemoveFromHistory}
        onClearHistory={onClearHistory}
      />
    </div>
  )
}
