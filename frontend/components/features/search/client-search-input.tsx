'use client'

import { useState, useEffect } from 'react'
import { Search } from 'lucide-react'
import { EnhancedSearchInput } from './enhanced-search-input'
import { Input } from '@/components/ui/input'
import { SearchHistoryItem } from '@/lib/utils/storage'
import { PaperSource } from '@/lib/types'

interface ClientSearchInputProps {
  value: string
  onChange: (value: string) => void
  onSubmit: () => void
  placeholder?: string
  disabled?: boolean
  searchHistory: SearchHistoryItem[]
  onSelectFromHistory: (query: string, sources: PaperSource[]) => void
  onRemoveFromHistory: (searchId: string) => void
  onClearHistory: () => void
  className?: string
  autoFocus?: boolean
}

export function ClientSearchInput(props: ClientSearchInputProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Show a simple input during SSR and initial hydration
  if (!isClient) {
    return (
      <div className="relative">
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10">
          <Search className="w-5 h-5 text-muted-foreground" />
        </div>
        <Input
          type="text"
          value={props.value}
          onChange={(e) => props.onChange(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault()
              props.onSubmit()
            }
          }}
          placeholder={props.placeholder}
          disabled={props.disabled}
          autoComplete="off"
          autoCorrect="off"
          autoCapitalize="off"
          spellCheck="false"
          className="pl-10 pr-4 text-lg h-12"
        />
      </div>
    )
  }

  // Show the enhanced input after hydration
  return <EnhancedSearchInput {...props} />
}
