import React from 'react'
import Image from 'next/image'
import { Filter } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { PaperSource } from '@/lib/types'

interface SourceFilterProps {
  selectedSources: PaperSource[]
  onSourceChange: (sources: PaperSource[]) => void
  disabled?: boolean
}

const SOURCE_CONFIG = {
  [PaperSource.ARXIV]: {
    label: 'arXiv',
    description: 'Preprint repository for scientific papers',
    color: 'text-orange-600',
    bgColor: 'bg-orange-50'
  },
  [PaperSource.CROSSREF]: {
    label: 'Crossref',
    description: 'Academic metadata and citations',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50'
  },
  [PaperSource.ASM]: {
    label: 'ASM',
    description: 'American Society for Microbiology',
    color: 'text-green-600',
    bgColor: 'bg-green-50'
  },
  [PaperSource.GOOGLE_SCHOLAR]: {
    label: 'Google Scholar',
    description: 'Broad academic search engine',
    color: 'text-purple-600',
    bgColor: 'bg-purple-50'
  },
  [PaperSource.IEEE]: {
    label: 'IEEE Xplore',
    description: 'Engineering and technology papers',
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-50'
  },
  [PaperSource.PUBMED]: {
    label: 'PubMed',
    description: 'Biomedical and life sciences',
    color: 'text-red-600',
    bgColor: 'bg-red-50'
  },
  [PaperSource.SCIHUB]: {
    label: 'Sci-Hub',
    description: 'Open access research papers',
    color: 'text-gray-600',
    bgColor: 'bg-gray-50'
  }
}

export function SourceFilter({ selectedSources, onSourceChange, disabled = false }: SourceFilterProps) {
  const allSources = Object.values(PaperSource)
  
  const handleSourceToggle = (source: PaperSource) => {
    if (disabled) return
    
    const newSources = selectedSources.includes(source)
      ? selectedSources.filter(s => s !== source)
      : [...selectedSources, source]
    
    onSourceChange(newSources)
  }

  const handleSelectAll = () => {
    if (disabled) return
    onSourceChange(allSources)
  }

  const handleClearAll = () => {
    if (disabled) return
    onSourceChange([])
  }

  const isAllSelected = selectedSources.length === allSources.length
  const isNoneSelected = selectedSources.length === 0

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filter by Source
          </CardTitle>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
              disabled={disabled || isAllSelected}
            >
              Select All
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearAll}
              disabled={disabled || isNoneSelected}
            >
              Clear All
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
          {allSources.map((source) => {
            const config = SOURCE_CONFIG[source]
            const isSelected = selectedSources.includes(source)
            
            return (
              <div
                key={source}
                className={`
                  relative p-3 rounded-lg border transition-all duration-200 cursor-pointer
                  ${isSelected 
                    ? `${config.bgColor} border-primary shadow-sm` 
                    : 'bg-background border-border hover:bg-accent hover:border-accent-foreground/20'
                  }
                  ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-sm'}
                `}
                onClick={() => handleSourceToggle(source)}
              >
                <div className="flex items-start gap-3">
                  <Checkbox
                    checked={isSelected}
                    disabled={disabled}
                    className="mt-0.5"
                    onClick={(e) => e.stopPropagation()}
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Image
                        src={`/logos/${source}.svg`}
                        alt={config.label}
                        width={16}
                        height={16}
                        className="flex-shrink-0"
                      />
                      <span className="font-medium text-sm truncate">
                        {config.label}
                      </span>
                    </div>
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {config.description}
                    </p>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
        
        {selectedSources.length > 0 && (
          <div className="mt-4 pt-3 border-t">
            <div className="text-sm text-muted-foreground">
              <span className="font-medium">{selectedSources.length}</span> of {allSources.length} sources selected
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
