'use client'

import React, { useEffect } from 'react'
import { Download, CheckSquare, Square, RotateCcw, Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

interface SearchResultsHeaderProps {
  totalResults: number
  selectedCount: number
  searchTime: number
  onSelectAll: () => void
  onClearSelection: () => void
  onBulkDownload: () => void
  isCreatingTask: boolean
  totalActiveDownloads: number
  onShowDownloads: () => void
  className?: string
}

export function SearchResultsHeader({
  totalResults,
  selectedCount,
  searchTime,
  onSelectAll,
  onClearSelection,
  onBulkDownload,
  isCreatingTask,
  totalActiveDownloads,
  onShowDownloads,
  className,
}: SearchResultsHeaderProps) {
  const isAllSelected = selectedCount === totalResults && totalResults > 0
  const hasSelection = selectedCount > 0

  // Keyboard shortcuts for bulk actions
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle if no input is focused
      if (document.activeElement?.tagName === 'INPUT' || document.activeElement?.tagName === 'TEXTAREA') {
        return
      }

      if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
        e.preventDefault()
        onSelectAll()
      } else if (e.key === 'Escape' && hasSelection) {
        e.preventDefault()
        onClearSelection()
      } else if ((e.ctrlKey || e.metaKey) && e.key === 'd' && hasSelection) {
        e.preventDefault()
        onBulkDownload()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onSelectAll, onClearSelection, onBulkDownload, hasSelection])

  return (
    <Card className={cn('border-l-4 border-l-primary/50', className)}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* Results Info */}
          <div className="flex items-center gap-4">
            <div className="text-sm text-muted-foreground">
              <span className="font-medium text-foreground">{totalResults.toLocaleString()}</span> papers found
              {searchTime > 0 && (
                <span className="ml-2">
                  • <span className="font-medium">{searchTime.toFixed(2)}s</span> search time
                </span>
              )}
            </div>
            
            {hasSelection && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <CheckSquare className="w-3 h-3" />
                {selectedCount} selected
              </Badge>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            {/* Active Downloads Indicator */}
            {totalActiveDownloads > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={onShowDownloads}
                className="flex items-center gap-2"
              >
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                <Download className="w-4 h-4" />
                <span>{totalActiveDownloads} downloading</span>
              </Button>
            )}

            {/* Selection Actions */}
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={isAllSelected ? onClearSelection : onSelectAll}
                className="flex items-center gap-2"
                title={isAllSelected ? "Clear selection (Esc)" : "Select all (Ctrl+A)"}
              >
                {isAllSelected ? (
                  <>
                    <Square className="w-4 h-4" />
                    <span className="hidden sm:inline">Clear All</span>
                  </>
                ) : (
                  <>
                    <CheckSquare className="w-4 h-4" />
                    <span className="hidden sm:inline">Select All</span>
                  </>
                )}
              </Button>

              {hasSelection && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onClearSelection}
                    title="Clear selection (Esc)"
                    className="flex items-center gap-2"
                  >
                    <RotateCcw className="w-4 h-4" />
                    <span className="hidden sm:inline">Clear</span>
                  </Button>

                  <Button
                    onClick={onBulkDownload}
                    disabled={isCreatingTask}
                    size="sm"
                    className="flex items-center gap-2"
                    title={`Download ${selectedCount} papers (Ctrl+D)`}
                  >
                    {isCreatingTask ? (
                      <div className="spinner w-4 h-4" />
                    ) : (
                      <Download className="w-4 h-4" />
                    )}
                    <span>Download ({selectedCount})</span>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Keyboard Shortcuts Hint */}
        {totalResults > 0 && (
          <div className="mt-3 pt-3 border-t border-border/50">
            <div className="flex flex-wrap items-center gap-4 text-xs text-muted-foreground">
              <span className="font-medium">Quick actions:</span>
              <div className="flex items-center gap-1">
                <kbd className="px-1.5 py-0.5 bg-muted rounded border text-xs">Ctrl</kbd>
                <span>+</span>
                <kbd className="px-1.5 py-0.5 bg-muted rounded border text-xs">A</kbd>
                <span>Select all</span>
              </div>
              <div className="flex items-center gap-1">
                <kbd className="px-1.5 py-0.5 bg-muted rounded border text-xs">Space</kbd>
                <span>Toggle selection</span>
              </div>
              <div className="flex items-center gap-1">
                <kbd className="px-1.5 py-0.5 bg-muted rounded border text-xs">Enter</kbd>
                <span>Download</span>
              </div>
              <div className="flex items-center gap-1">
                <kbd className="px-1.5 py-0.5 bg-muted rounded border text-xs">↑</kbd>
                <kbd className="px-1.5 py-0.5 bg-muted rounded border text-xs">↓</kbd>
                <span>Navigate</span>
              </div>
              <div className="flex items-center gap-1">
                <kbd className="px-1.5 py-0.5 bg-muted rounded border text-xs">?</kbd>
                <span>Help</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
