// Enums
export enum SearchStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export enum PaperSource {
  ARXIV = 'arxiv',
  CROSSREF = 'crossref',
  ASM = 'asm',
  GOOGLE_SCHOLAR = 'google_scholar',
  IEEE = 'ieee',
  PUBMED = 'pubmed',
  SCIHUB = 'scihub'
}

export enum DownloadStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// Data Transfer Objects (Models)
export interface AuthorDTO {
  name: string
  affiliation?: string
  email?: string
}

export interface PaperDTO {
  id: string
  title: string
  authors: AuthorDTO[]
  abstract?: string
  doi?: string
  url?: string
  published_date?: string
  source: PaperSource
  pdf_url?: string
  citation_count?: number
  keywords?: string[]
  journal?: string
  volume?: string
  issue?: string
  pages?: string
}

export interface SearchRequestDTO {
  query: string
  sources: PaperSource[]
  max_results?: number
  filters?: {
    year_start?: number
    year_end?: number
    journal?: string
    author?: string
  }
}

export interface SearchResultDTO {
  papers: PaperDTO[]
  total_found: number
  search_time: number
  status: SearchStatus
  error_message?: string
}

export interface DownloadTaskDTO {
  id: string
  paper_ids: string[]
  status: DownloadStatus
  progress: number
  total_papers: number
  completed_papers: number
  failed_papers: number
  download_urls: string[]
  created_at: string
  updated_at: string
  error_message?: string
}

export interface DownloadProgressDTO {
  task_id: string
  status: DownloadStatus
  progress: number
  current_paper?: PaperDTO
  message?: string
  error?: string
}

export interface ActiveDownload {
  id: string
  type: 'bulk' | 'single'
  status: string
  progress: number
  title?: string
  paper_id?: string
  total_papers?: number
  completed_papers?: number
  failed_papers?: number
  created_at: string
}

export interface SingleDownloadTask {
  id: string
  paper_id: string
  title: string
  status: DownloadStatus
  progress: number
  created_at: string
  type: 'single'
}

export interface PaperDownloadData {
  id: string
  title: string
  doi?: string
  url?: string
  pdf_url?: string
  abstract?: string
  authors: { name: string }[]
  published_date?: string
  journal?: string
  keywords: string[]
  source: PaperSource
  citation_count?: number
}

// UI State Types
export interface SearchFormData {
  query: string
  sources: PaperSource[]
  maxResults: number
  yearStart?: number
  yearEnd?: number
  journal?: string
  author?: string
}

export interface UINotification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
}

// API Response Types
export interface ApiResponse<T> {
  data?: T
  error?: string
  status: 'success' | 'error'
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// WebSocket Message Types
export interface WebSocketMessage<T = unknown> {
  type: string
  data?: T
  message?: string
}

export interface DownloadProgressMessage
  extends WebSocketMessage<DownloadProgressDTO> {
  type: 'download_progress'
}

export interface ActiveDownloadsResponse {
  active_downloads: ActiveDownload[]
  total_active: number
}

export interface DownloadTasksResponse {
  tasks: DownloadTaskDTO[]
  total_count: number
}
