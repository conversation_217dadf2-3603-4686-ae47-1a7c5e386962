import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { 
  SearchRequestDTO, 
  SearchResultDTO,
  DownloadTaskDTO,
  PaperSource,
  ApiResponse,
  SearchStatus,
  PaperDownloadData,
  ActiveDownloadsResponse,
  DownloadTasksResponse
} from '../types'

class ApiService {
  private api: AxiosInstance

  constructor() {
    this.api = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error) => {
        console.error('API Request Error:', error)
        return Promise.reject(error)
      }
    )

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`API Response: ${response.status} ${response.config.url}`)
        return response
      },
      (error) => {
        console.error('API Response Error:', error.response?.data || error.message)
        
        // Handle common errors
        if (error.response?.status === 401) {
          console.warn('Unauthorized access detected')
        } else if (error.response?.status >= 500) {
          console.error('Server error detected')
        } else if (error.code === 'ECONNREFUSED') {
          console.error('Connection refused - is the backend server running?')
        } else if (error.code === 'NETWORK_ERROR') {
          console.error('Network error - check your connection')
        }
        
        return Promise.reject(error)
      }
    )
  }

  // Helper method to validate and process API responses
  private processApiResponse<T>(response: AxiosResponse): T {
    try {
      // Check if response has the expected structure
      if (!response.data) {
        throw new Error('No data in response')
      }

      // Check if this is a SearchResultDTO (has papers field) - handle directly
      if ('papers' in response.data && Array.isArray(response.data.papers)) {
        return response.data as T
      }

      // Check if this is other direct response types (no API wrapper)
      if (!('status' in response.data) || typeof response.data.status !== 'string') {
        return response.data as T
      }

      // Check if this has the SearchStatus enum values (search response)
      if (response.data.status === 'completed' || response.data.status === 'failed' || 
          response.data.status === 'pending' || response.data.status === 'in_progress') {
        return response.data as T
      }

      // If response.data has ApiResponse structure (status is 'success' or 'error')
      const apiResponse = response.data as ApiResponse<T>
      
      if (apiResponse.status === 'error') {
        throw new Error(apiResponse.error || 'API returned error status')
      }
      
      if (!apiResponse.data) {
        throw new Error('API response missing data field')
      }
      
      return apiResponse.data
    } catch (error) {
      console.error('Error processing API response:', error)
      throw error
    }
  }

  // Search papers
  async searchPapers(searchRequest: SearchRequestDTO): Promise<SearchResultDTO> {
    try {
      const response = await this.api.post('/papers/search/', searchRequest)
      
      // Process the response with error handling
      const data = this.processApiResponse<SearchResultDTO>(response)
      
      // Validate the search result structure
      if (!data) {
        throw new Error('Search returned no data')
      }

      // Ensure required fields exist with defaults
      const searchResult: SearchResultDTO = {
        papers: data.papers || [],
        total_found: data.total_found || 0,
        search_time: data.search_time || 0,
        status: data.status || SearchStatus.FAILED,
        error_message: data.error_message,
      }

      return searchResult
    } catch (error) {
      console.error('Search papers error:', error)
      
      // Return a failed search result instead of throwing
      const failedResult: SearchResultDTO = {
        papers: [],
        total_found: 0,
        search_time: 0,
        status: SearchStatus.FAILED,
        error_message: error instanceof Error ? error.message : 'Search failed',
      }
      
      return failedResult
    }
  }

  // Create download task
  async createDownloadTask(
    paperIds: string[],
    papersData?: PaperDownloadData[]
  ): Promise<DownloadTaskDTO> {
    try {
      const response = await this.api.post('/papers/download/', { 
        paper_ids: paperIds,
        papers_data: papersData || []
      })
      return this.processApiResponse<DownloadTaskDTO>(response)
    } catch (error) {
      console.error('Create download task error:', error)
      throw new Error(error instanceof Error ? error.message : 'Download task creation failed')
    }
  }

  // Get download task status
  async getDownloadTaskStatus(taskId: string): Promise<DownloadTaskDTO> {
    try {
      const response = await this.api.get(`/papers/download/${taskId}/status/`)
      return this.processApiResponse<DownloadTaskDTO>(response)
    } catch (error) {
      console.error('Get download task status error:', error)
      throw new Error(error instanceof Error ? error.message : 'Failed to get download status')
    }
  }

  // Get available paper sources
  async getAvailableSources(): Promise<PaperSource[]> {
    try {
      const response = await this.api.get('/papers/sources/')
      const data = this.processApiResponse<PaperSource[]>(response)
      
      // Validate that we got an array
      if (!Array.isArray(data)) {
        console.warn('Expected array of sources, got:', typeof data)
        return []
      }
      
      return data
    } catch (error) {
      console.error('Get available sources error:', error)
      // Return empty array instead of throwing
      return []
    }
  }

  // Download file
  async downloadFile(url: string, filename: string): Promise<void> {
    try {
      const response = await this.api.get(url, {
        responseType: 'blob',
      })
      
      // Create blob link to download
      const blob = new Blob([response.data])
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      link.click()
      
      // Clean up
      window.URL.revokeObjectURL(link.href)
    } catch (error) {
      console.error('Download file error:', error)
      throw new Error(error instanceof Error ? error.message : 'Download failed')
    }
  }

  // Download single paper
  async downloadSinglePaper(paperId: string, paperData: { title: string; pdf_url?: string; doi?: string; source: string }): Promise<{ id: string; created_at: string }> {
    try {
      const response = await this.api.post('/papers/download/single/', {
        paper_id: paperId,
        paper_data: paperData
      })
      return this.processApiResponse<{ id: string; created_at: string }>(response)
    } catch (error) {
      console.error('Download single paper error:', error)
      throw new Error(error instanceof Error ? error.message : 'Single paper download failed')
    }
  }

  // Get active downloads
  async getActiveDownloads(): Promise<ActiveDownloadsResponse> {
    try {
      const response = await this.api.get('/papers/download/active/')
      return this.processApiResponse<ActiveDownloadsResponse>(response)
    } catch (error) {
      console.error('Get active downloads error:', error)
      // Return empty state instead of throwing
      return { active_downloads: [], total_active: 0 }
    }
  }

  // Get all download tasks
  async getAllDownloadTasks(): Promise<DownloadTasksResponse> {
    try {
      const response = await this.api.get('/papers/download/tasks/')
      return this.processApiResponse<DownloadTasksResponse>(response)
    } catch (error) {
      console.error('Get all download tasks error:', error)
      // Return empty state instead of throwing
      return { tasks: [], total_count: 0 }
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.api.get('/health/')
      return response.status === 200
    } catch (error) {
      console.error('Health check error:', error)
      return false
    }
  }
}

// Export singleton instance
export const apiService = new ApiService()
export default apiService
