import { DownloadProgressDTO, WebSocketMessage } from '../types'

export type ProgressCallback = (progress: DownloadProgressDTO) => void
export type ErrorCallback = (error: string) => void
export type ConnectionCallback = () => void

class WebSocketService {
  private socket: WebSocket | null = null
  private progressCallbacks: Map<string, ProgressCallback> = new Map()
  private errorCallbacks: Set<ErrorCallback> = new Set()
  private connectionCallbacks: Set<ConnectionCallback> = new Set()
  private isConnected = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectTimeout: NodeJS.Timeout | null = null
  private isDevMode: boolean = process.env.NODE_ENV === 'development'

  constructor() {
    // Only attempt to connect if not in development mode or if explicitly enabled
    if (!this.isDevMode || process.env.NEXT_PUBLIC_WS_ENABLED === 'true') {
      this.connect()
    } else {
      console.log('WebSocket disabled in development mode. Set NEXT_PUBLIC_WS_ENABLED=true to enable.')
    }
  }

  private connect(): void {
    try {
      // Convert HTTP URL to WebSocket URL
      const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/ws/downloads/'
      
      this.socket = new WebSocket(wsUrl)
      this.setupEventListeners()
    } catch (error) {
      if (!this.isDevMode) {
        console.error('WebSocket connection error:', error)
        this.notifyError('Failed to connect to WebSocket server')
      } else {
        console.warn('WebSocket connection failed (development mode):', error)
      }
      this.scheduleReconnect()
    }
  }

  private setupEventListeners(): void {
    if (!this.socket) return

    this.socket.onopen = () => {
      console.log('WebSocket connected')
      this.isConnected = true
      this.reconnectAttempts = 0
      this.connectionCallbacks.forEach(callback => callback())
    }

    this.socket.onclose = (event) => {
      if (!this.isDevMode) {
        console.log('WebSocket disconnected:', event.code, event.reason)
      }
      this.isConnected = false
      
      // Only attempt to reconnect if it wasn't a clean close
      if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
        this.scheduleReconnect()
      }
    }

    this.socket.onerror = (error) => {
      // WebSocket error events often don't contain meaningful error information
      let errorMessage = 'WebSocket connection failed'
      
      try {
        if (error && typeof error === 'object') {
          // Check for various error properties
          if ('message' in error && error.message) {
            errorMessage = String(error.message)
          } else if ('reason' in error && error.reason) {
            errorMessage = String(error.reason)
          } else if ('error' in error && error.error) {
            errorMessage = String(error.error)
          } else {
            // If no useful error info, provide a generic message based on connection state
            if (!this.isConnected && this.reconnectAttempts === 0) {
              errorMessage = 'Failed to establish WebSocket connection'
            } else {
              errorMessage = 'WebSocket connection error occurred'
            }
          }
        }
      } catch (parseError) {
        console.warn('Error parsing WebSocket error event:', parseError)
        errorMessage = 'WebSocket connection error (unable to parse error details)'
      }
      
      // Reduce error noise in development mode
      if (this.isDevMode) {
        console.warn('WebSocket error (development mode):', errorMessage)
      } else {
        console.error('WebSocket error:', errorMessage)
        this.notifyError(errorMessage)
      }
    }

    this.socket.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data)
        this.handleMessage(message)
      } catch (error) {
        console.error('Error parsing WebSocket message:', error)
        if (!this.isDevMode) {
          this.notifyError('Failed to parse WebSocket message')
        }
      }
    }
  }

  private handleMessage(message: WebSocketMessage): void {
    try {
      switch (message.type) {
        case 'download_progress':
          if (message.data) {
            const progress = message.data as DownloadProgressDTO
            console.log('Download progress received:', progress)
            const callback = this.progressCallbacks.get(progress.task_id)
            if (callback) {
              callback(progress)
            }
          } else {
            console.warn('Received download_progress message without data')
          }
          break
        
        case 'error':
          const errorMessage = message.message || 'Unknown WebSocket error'
          console.error('WebSocket error message:', errorMessage)
          this.notifyError(errorMessage)
          break
        
        default:
          console.log('Unknown WebSocket message type:', message.type)
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error)
      if (!this.isDevMode) {
        this.notifyError('Failed to handle WebSocket message')
      }
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
    }

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000)
      
      if (!this.isDevMode) {
        console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`)
      }
      
      this.reconnectTimeout = setTimeout(() => {
        this.reconnectAttempts++
        this.connect()
      }, delay)
    } else {
      if (!this.isDevMode) {
        console.error('Maximum WebSocket reconnection attempts reached')
        this.notifyError('WebSocket connection failed after multiple attempts')
      }
    }
  }

  // Subscribe to download progress updates for a specific task
  subscribeToProgress(taskId: string, callback: ProgressCallback): void {
    this.progressCallbacks.set(taskId, callback)
    
    // Send join message if connected
    if (this.socket && this.isConnected) {
      this.sendMessage('join_task', { task_id: taskId })
    }
  }

  // Unsubscribe from download progress updates
  unsubscribeFromProgress(taskId: string): void {
    this.progressCallbacks.delete(taskId)
    
    // Send leave message if connected
    if (this.socket && this.isConnected) {
      this.sendMessage('leave_task', { task_id: taskId })
    }
  }

  // Subscribe to error notifications
  subscribeToErrors(callback: ErrorCallback): void {
    this.errorCallbacks.add(callback)
  }

  // Unsubscribe from error notifications
  unsubscribeFromErrors(callback: ErrorCallback): void {
    this.errorCallbacks.delete(callback)
  }

  // Subscribe to connection status changes
  subscribeToConnection(callback: ConnectionCallback): void {
    this.connectionCallbacks.add(callback)
  }

  // Unsubscribe from connection status changes
  unsubscribeFromConnection(callback: ConnectionCallback): void {
    this.connectionCallbacks.delete(callback)
  }

  private notifyError(error: string): void {
    this.errorCallbacks.forEach(callback => {
      try {
        callback(error)
      } catch (callbackError) {
        console.error('Error in WebSocket error callback:', callbackError)
      }
    })
  }

  // Check if WebSocket is connected
  getConnectionStatus(): boolean {
    return this.isConnected
  }

  // Manually reconnect
  reconnect(): void {
    if (this.socket) {
      this.socket.close()
    }
    this.reconnectAttempts = 0
    this.connect()
  }

  // Send a message to the WebSocket server
  sendMessage<T>(type: string, data: T): void {
    if (this.socket && this.isConnected) {
      try {
        const message: WebSocketMessage<T> = { type, data }
        this.socket.send(JSON.stringify(message))
      } catch (error) {
        console.error('Error sending WebSocket message:', error)
        if (!this.isDevMode) {
          this.notifyError('Failed to send WebSocket message')
        }
      }
    } else {
      if (!this.isDevMode) {
        console.warn('WebSocket not connected, message not sent:', { type, data })
      }
    }
  }

  // Cleanup
  disconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }
    
    if (this.socket) {
      this.socket.close(1000, 'Client disconnect')
      this.socket = null
    }
    
    this.progressCallbacks.clear()
    this.errorCallbacks.clear()
    this.connectionCallbacks.clear()
    this.isConnected = false
  }
}

// Export singleton instance
export const webSocketService = new WebSocketService()
export default webSocketService
