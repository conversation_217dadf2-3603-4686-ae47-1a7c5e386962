/**
 * Local storage utilities for persisting user preferences and search history
 */

import { PaperSource } from '../types'

// Storage keys
const STORAGE_KEYS = {
  SEARCH_HISTORY: 'papermagic_search_history',
  PREFERRED_SOURCES: 'papermagic_preferred_sources',
  USER_PREFERENCES: 'papermagic_user_preferences',
} as const

// Types
export interface SearchHistoryItem {
  id: string
  query: string
  sources: PaperSource[]
  timestamp: number
  resultsCount?: number
}

export interface UserPreferences {
  preferredSources: PaperSource[]
  maxResults: number
  theme?: 'light' | 'dark' | 'system'
}

// Default preferences
const DEFAULT_PREFERENCES: UserPreferences = {
  preferredSources: [PaperSource.ARXIV, PaperSource.CROSSREF, PaperSource.ASM],
  maxResults: 20,
  theme: 'system',
}

/**
 * Safe localStorage wrapper that handles SSR and errors
 */
class SafeStorage {
  private isAvailable(): boolean {
    try {
      return typeof window !== 'undefined' && 'localStorage' in window
    } catch {
      return false
    }
  }

  get<T>(key: string, defaultValue: T): T {
    if (!this.isAvailable()) return defaultValue
    
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.warn(`Failed to get item from localStorage: ${key}`, error)
      return defaultValue
    }
  }

  set<T>(key: string, value: T): boolean {
    if (!this.isAvailable()) return false
    
    try {
      localStorage.setItem(key, JSON.stringify(value))
      return true
    } catch (error) {
      console.warn(`Failed to set item in localStorage: ${key}`, error)
      return false
    }
  }

  remove(key: string): boolean {
    if (!this.isAvailable()) return false
    
    try {
      localStorage.removeItem(key)
      return true
    } catch (error) {
      console.warn(`Failed to remove item from localStorage: ${key}`, error)
      return false
    }
  }

  clear(): boolean {
    if (!this.isAvailable()) return false
    
    try {
      localStorage.clear()
      return true
    } catch (error) {
      console.warn('Failed to clear localStorage', error)
      return false
    }
  }
}

const storage = new SafeStorage()

/**
 * Search History Management
 */
export const searchHistoryManager = {
  /**
   * Get search history
   */
  getHistory(): SearchHistoryItem[] {
    return storage.get(STORAGE_KEYS.SEARCH_HISTORY, [])
  },

  /**
   * Add a search to history
   */
  addSearch(query: string, sources: PaperSource[], resultsCount?: number): void {
    const history = this.getHistory()
    
    // Remove duplicate if exists
    const filteredHistory = history.filter(item => 
      item.query.toLowerCase() !== query.toLowerCase()
    )
    
    // Add new search at the beginning
    const newSearch: SearchHistoryItem = {
      id: `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      query: query.trim(),
      sources,
      timestamp: Date.now(),
      resultsCount,
    }
    
    // Keep only last 20 searches
    const updatedHistory = [newSearch, ...filteredHistory].slice(0, 20)
    
    storage.set(STORAGE_KEYS.SEARCH_HISTORY, updatedHistory)
  },

  /**
   * Remove a search from history
   */
  removeSearch(searchId: string): void {
    const history = this.getHistory()
    const updatedHistory = history.filter(item => item.id !== searchId)
    storage.set(STORAGE_KEYS.SEARCH_HISTORY, updatedHistory)
  },

  /**
   * Clear all search history
   */
  clearHistory(): void {
    storage.remove(STORAGE_KEYS.SEARCH_HISTORY)
  },

  /**
   * Get recent unique queries (for autocomplete)
   */
  getRecentQueries(limit: number = 10): string[] {
    const history = this.getHistory()
    const uniqueQueries = Array.from(new Set(
      history.map(item => item.query)
    ))
    return uniqueQueries.slice(0, limit)
  },
}

/**
 * User Preferences Management
 */
export const preferencesManager = {
  /**
   * Get user preferences
   */
  getPreferences(): UserPreferences {
    return storage.get(STORAGE_KEYS.USER_PREFERENCES, DEFAULT_PREFERENCES)
  },

  /**
   * Update user preferences
   */
  updatePreferences(preferences: Partial<UserPreferences>): void {
    const current = this.getPreferences()
    const updated = { ...current, ...preferences }
    storage.set(STORAGE_KEYS.USER_PREFERENCES, updated)
  },

  /**
   * Get preferred sources
   */
  getPreferredSources(): PaperSource[] {
    return this.getPreferences().preferredSources
  },

  /**
   * Set preferred sources
   */
  setPreferredSources(sources: PaperSource[]): void {
    this.updatePreferences({ preferredSources: sources })
  },

  /**
   * Reset preferences to default
   */
  resetPreferences(): void {
    storage.set(STORAGE_KEYS.USER_PREFERENCES, DEFAULT_PREFERENCES)
  },
}

/**
 * Export storage instance for direct access if needed
 */
export { storage }
