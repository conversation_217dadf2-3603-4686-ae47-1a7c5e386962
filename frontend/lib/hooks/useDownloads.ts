import { useState, useCallback, useEffect } from 'react'
import {
  DownloadTaskDTO,
  DownloadProgressDTO,
  DownloadStatus,
  PaperDTO,
  ActiveDownload,
  SingleDownloadTask
} from '../types'
import { apiService } from '../services/api'
import { webSocketService } from '../services/websocket'


interface UseDownloadsReturn {
  // Download tasks state
  downloadTasks: DownloadTaskDTO[]
  isCreatingTask: boolean
  
  // Current task being monitored
  currentTask: DownloadTaskDTO | null
  currentProgress: DownloadProgressDTO | null
  
  // Single downloads
  singleDownloads: SingleDownloadTask[]
  isDownloadingSingle: boolean
  
  // Active downloads (global progress)
  activeDownloads: ActiveDownload[]
  totalActiveDownloads: number
  globalProgress: number
  
  // Actions
  createDownloadTask: (paperIds: string[], papers?: PaperDTO[]) => Promise<DownloadTaskDTO | null>
  downloadSinglePaper: (paper: PaperDTO) => Promise<SingleDownloadTask | null>
  monitorTask: (taskId: string) => void
  stopMonitoring: () => void
  refreshTaskStatus: (taskId: string) => Promise<void>
  refreshActiveDownloads: () => Promise<void>
  loadDownloadTasks: () => Promise<void>
  refreshAllDownloads: () => Promise<void>
  downloadFile: (url: string, filename: string) => Promise<void>
  
  // Utility functions
  getTaskProgress: (taskId: string) => number
  getTaskStatus: (taskId: string) => DownloadStatus
  isTaskCompleted: (taskId: string) => boolean
  isTaskFailed: (taskId: string) => boolean
  getCompletedDownloads: () => DownloadTaskDTO[]
  getActiveDownloads: () => DownloadTaskDTO[]
}

export const useDownloads = (): UseDownloadsReturn => {
  // State
  const [downloadTasks, setDownloadTasks] = useState<DownloadTaskDTO[]>([])
  const [isCreatingTask, setIsCreatingTask] = useState(false)
  const [currentTask, setCurrentTask] = useState<DownloadTaskDTO | null>(null)
  const [currentProgress, setCurrentProgress] = useState<DownloadProgressDTO | null>(null)
  
  // Single downloads state
  const [singleDownloads, setSingleDownloads] = useState<SingleDownloadTask[]>([])
  const [isDownloadingSingle, setIsDownloadingSingle] = useState(false)
  
  // Active downloads state
  const [activeDownloads, setActiveDownloads] = useState<ActiveDownload[]>([])

  // Refresh active downloads
  const refreshActiveDownloads = useCallback(async () => {
    try {
      const response = await apiService.getActiveDownloads()
      setActiveDownloads(response.active_downloads || [])
    } catch (error) {
      console.error('Failed to refresh active downloads:', error)
    }
  }, [])

  // Monitor task progress via WebSocket
  const monitorTask = useCallback((taskId: string) => {
    // Find the task
    const task = downloadTasks.find(t => t.id === taskId)
    if (task) {
      setCurrentTask(task)
    }

    // Subscribe to progress updates
    webSocketService.subscribeToProgress(taskId, (progress: DownloadProgressDTO) => {
      setCurrentProgress(progress)
      
      // Update the task in the list
      setDownloadTasks(prev => prev.map(task => 
        task.id === taskId 
          ? { 
              ...task, 
              status: progress.status, 
              progress: progress.progress,
              updated_at: new Date().toISOString()
            }
          : task
      ))
      
      // Refresh active downloads to update global progress
      refreshActiveDownloads()
    })
  }, [downloadTasks, refreshActiveDownloads])

  // Create download task
  const createDownloadTask = useCallback(
    async (paperIds: string[], papers?: PaperDTO[]): Promise<DownloadTaskDTO | null> => {
    if (paperIds.length === 0) {
      console.warn('No papers selected for download')
      return null
    }

    setIsCreatingTask(true)
    try {
      // Convert papers to the format expected by backend
      const papersData = papers?.filter(paper => paperIds.includes(paper.id)).map(paper => ({
        id: paper.id,
        title: paper.title,
        doi: paper.doi,
        url: paper.url,
        pdf_url: paper.pdf_url,
        abstract: paper.abstract,
        authors: paper.authors?.map(author => ({ name: author.name })) || [],
        published_date: paper.published_date,
        journal: paper.journal,
        keywords: paper.keywords || [],
        source: paper.source,
        citation_count: paper.citation_count
      })) || []

      const task = await apiService.createDownloadTask(paperIds, papersData)
      
      // Add to tasks list
      setDownloadTasks(prev => [task, ...prev])
      
      // Start monitoring this task
      monitorTask(task.id)
      
      // Refresh active downloads
      refreshActiveDownloads()
      
      return task
    } catch (error) {
      console.error('Failed to create download task:', error)
      return null
    } finally {
      setIsCreatingTask(false)
    }
  }, [monitorTask, refreshActiveDownloads])

  // Download single paper
  const downloadSinglePaper = useCallback(
    async (paper: PaperDTO): Promise<SingleDownloadTask | null> => {
    setIsDownloadingSingle(true)
    try {
      const response = await apiService.downloadSinglePaper(paper.id, {
        title: paper.title,
        pdf_url: paper.pdf_url,
        doi: paper.doi,
        source: paper.source
      })
      
      const singleTask: SingleDownloadTask = {
        id: response.id,
        paper_id: paper.id,
        title: paper.title,
        status: DownloadStatus.PENDING,
        progress: 0,
        created_at: response.created_at,
        type: 'single'
      }
      
      // Add to single downloads list
      setSingleDownloads(prev => [singleTask, ...prev])
      
      // Refresh active downloads to show this new download
      refreshActiveDownloads()
      
      return singleTask
    } catch (error) {
      console.error('Failed to download single paper:', error)
      return null
    } finally {
      setIsDownloadingSingle(false)
    }
  }, [refreshActiveDownloads])

  // Load all download tasks
  const loadDownloadTasks = useCallback(async () => {
    try {
      const response = await apiService.getAllDownloadTasks()
      setDownloadTasks(response.tasks || [])
    } catch (error) {
      console.error('Failed to load download tasks:', error)
    }
  }, [])

  // Refresh all download data
  const refreshAllDownloads = useCallback(async () => {
    await Promise.all([
      refreshActiveDownloads(),
      loadDownloadTasks()
    ])
  }, [refreshActiveDownloads, loadDownloadTasks])

  // Stop monitoring current task
  const stopMonitoring = useCallback(() => {
    if (currentTask) {
      webSocketService.unsubscribeFromProgress(currentTask.id)
      setCurrentTask(null)
      setCurrentProgress(null)
    }
  }, [currentTask])

  // Refresh task status manually
  const refreshTaskStatus = useCallback(async (taskId: string) => {
    try {
      const updatedTask = await apiService.getDownloadTaskStatus(taskId)
      
      setDownloadTasks(prev => prev.map(task => 
        task.id === taskId ? updatedTask : task
      ))
      
      if (currentTask?.id === taskId) {
        setCurrentTask(updatedTask)
      }
    } catch (error) {
      console.error('Failed to refresh task status:', error)
    }
  }, [currentTask])

  // Download individual file
  const downloadFile = useCallback(async (url: string, filename: string) => {
    try {
      await apiService.downloadFile(url, filename)
    } catch (error) {
      console.error('Failed to download file:', error)
      throw error
    }
  }, [])

  // Utility functions
  const getTaskProgress = useCallback((taskId: string): number => {
    const task = downloadTasks.find(t => t.id === taskId)
    return task?.progress || 0
  }, [downloadTasks])

  const getTaskStatus = useCallback((taskId: string): DownloadStatus => {
    const task = downloadTasks.find(t => t.id === taskId)
    return task?.status || DownloadStatus.PENDING
  }, [downloadTasks])

  const isTaskCompleted = useCallback((taskId: string): boolean => {
    return getTaskStatus(taskId) === DownloadStatus.COMPLETED
  }, [getTaskStatus])

  const isTaskFailed = useCallback((taskId: string): boolean => {
    return getTaskStatus(taskId) === DownloadStatus.FAILED
  }, [getTaskStatus])

  const getCompletedDownloads = useCallback((): DownloadTaskDTO[] => {
    return downloadTasks.filter(task => task.status === DownloadStatus.COMPLETED)
  }, [downloadTasks])

  const getActiveDownloads = useCallback((): DownloadTaskDTO[] => {
    return downloadTasks.filter(task => 
      task.status === DownloadStatus.PENDING || 
      task.status === DownloadStatus.IN_PROGRESS
    )
  }, [downloadTasks])

  // Calculate global progress
  const globalProgress = activeDownloads.length > 0 
    ? activeDownloads.reduce((acc, download) => acc + download.progress, 0) / activeDownloads.length
    : 0

  const totalActiveDownloads = activeDownloads.length

  // Auto-refresh active downloads periodically
  useEffect(() => {
    const interval = setInterval(refreshAllDownloads, 5000) // Refresh every 5 seconds
    return () => clearInterval(interval)
  }, [refreshAllDownloads])

  // Initial load of all downloads
  useEffect(() => {
    refreshAllDownloads()
  }, [refreshAllDownloads])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopMonitoring()
    }
  }, [stopMonitoring])

  return {
    downloadTasks,
    isCreatingTask,
    currentTask,
    currentProgress,
    singleDownloads,
    isDownloadingSingle,
    activeDownloads,
    totalActiveDownloads,
    globalProgress,
    createDownloadTask,
    downloadSinglePaper,
    monitorTask,
    stopMonitoring,
    refreshTaskStatus,
    refreshActiveDownloads,
    loadDownloadTasks,
    refreshAllDownloads,
    downloadFile,
    getTaskProgress,
    getTaskStatus,
    isTaskCompleted,
    isTaskFailed,
    getCompletedDownloads,
    getActiveDownloads,
  }
}
