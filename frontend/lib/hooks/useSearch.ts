import { useState, useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import {
  SearchResultDTO,
  PaperSource,
  SearchStatus
} from '../types'
import { apiService } from '../services/api'
import {
  searchHistoryManager,
  preferencesManager,
  SearchHistoryItem
} from '../utils/storage'

// Form validation schema
const searchFormSchema = z.object({
  query: z.string().min(1, 'Query is required').max(500, 'Query too long'),
  sources: z.array(z.nativeEnum(PaperSource)).min(1, 'At least one source required'),
  maxResults: z.number().min(1).max(100).default(20),
  yearStart: z.number().optional(),
  yearEnd: z.number().optional(),
  journal: z.string().optional(),
  author: z.string().optional(),
})

export type SearchFormSchema = z.infer<typeof searchFormSchema>

interface UseSearchReturn {
  // Form management
  form: ReturnType<typeof useForm<SearchFormSchema>>

  // Search state
  isSearching: boolean
  searchResults: SearchResultDTO | null
  searchError: string | null

  // Search actions
  performSearch: (data: SearchFormSchema) => Promise<void>
  clearResults: () => void

  // Available sources
  availableSources: PaperSource[]
  isLoadingSources: boolean

  // Search history
  searchHistory: SearchHistoryItem[]
  addToHistory: (query: string, sources: PaperSource[], resultsCount?: number) => void
  removeFromHistory: (searchId: string) => void
  clearSearchHistory: () => void

  // User preferences
  preferredSources: PaperSource[]
  updatePreferredSources: (sources: PaperSource[]) => void

  // Utility functions
  getTotalResults: () => number
  getSearchTime: () => number
  hasResults: () => boolean
}

export const useSearch = (): UseSearchReturn => {
  // Default preferences for SSR
  const defaultSources = [PaperSource.ARXIV, PaperSource.CROSSREF, PaperSource.ASM]

  // Form management
  const form = useForm<SearchFormSchema>({
    resolver: zodResolver(searchFormSchema),
    defaultValues: {
      query: '',
      sources: defaultSources,
      maxResults: 20,
    },
  })

  // Search state
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<SearchResultDTO | null>(null)
  const [searchError, setSearchError] = useState<string | null>(null)

  // Available sources state
  const [availableSources, setAvailableSources] = useState<PaperSource[]>([])
  const [isLoadingSources, setIsLoadingSources] = useState(false)

  // Search history state
  const [searchHistory, setSearchHistory] = useState<SearchHistoryItem[]>([])

  // User preferences state - start with defaults for SSR
  const [preferredSources, setPreferredSources] = useState<PaperSource[]>(defaultSources)
  const [isHydrated, setIsHydrated] = useState(false)

  // Load available sources
  const loadAvailableSources = useCallback(async () => {
    setIsLoadingSources(true)
    try {
      const sources = await apiService.getAvailableSources()
      setAvailableSources(sources || [])
    } catch (error) {
      console.error('Failed to load available sources:', error)
      // Fallback to default sources
      setAvailableSources([
        PaperSource.ARXIV,
        PaperSource.CROSSREF,
        PaperSource.ASM,
        PaperSource.GOOGLE_SCHOLAR,
        PaperSource.IEEE,
        PaperSource.PUBMED,
      ])
    } finally {
      setIsLoadingSources(false)
    }
  }, [])

  // Search history management
  const addToHistory = useCallback((query: string, sources: PaperSource[], resultsCount?: number) => {
    searchHistoryManager.addSearch(query, sources, resultsCount)
    setSearchHistory(searchHistoryManager.getHistory())
  }, [])

  // Ultra-fast search with streaming results
  const performSearch = useCallback(async (data: SearchFormSchema) => {
    setIsSearching(true)
    setSearchError(null)

    // Initialize empty results for streaming
    setSearchResults({
      papers: [],
      total_found: 0,
      search_time: 0,
      status: SearchStatus.IN_PROGRESS
    })

    try {
      const searchRequest = {
        query: data.query,
        sources: data.sources,
        max_results: data.maxResults,
        filters: {
          year_start: data.yearStart,
          year_end: data.yearEnd,
          journal: data.journal,
          author: data.author,
        },
      }

      // Start search with streaming updates
      const startTime = Date.now()
      const results = await apiService.searchPapers(searchRequest)

      // Defensive check for results
      if (!results) {
        throw new Error('No results returned from search')
      }

      // Update with final results
      setSearchResults({
        ...results,
        search_time: (Date.now() - startTime) / 1000
      })

      // Add to search history if search was successful
      if (results.status === SearchStatus.COMPLETED && data.query.trim()) {
        addToHistory(data.query, data.sources, results.total_found)
      }

      // Check if search failed
      if (results.status === SearchStatus.FAILED) {
        const errorMessage = results.error_message || 'Search failed'
        setSearchError(errorMessage)
        console.error('Search failed:', errorMessage)
      }
    } catch (error) {
      let errorMessage = 'Search failed'

      if (error instanceof Error) {
        errorMessage = error.message
      } else if (typeof error === 'string') {
        errorMessage = error
      } else if (error && typeof error === 'object' && 'message' in error) {
        errorMessage = String(error.message)
      }

      setSearchError(errorMessage)
      console.error('Search error:', error)
    } finally {
      setIsSearching(false)
    }
  }, [addToHistory])

  // Clear results
  const clearResults = useCallback(() => {
    setSearchResults(null)
    setSearchError(null)
  }, [])

  // Utility functions
  const getTotalResults = useCallback(() => {
    return searchResults?.total_found || 0
  }, [searchResults])

  const getSearchTime = useCallback(() => {
    return searchResults?.search_time || 0
  }, [searchResults])

  const hasResults = useCallback(() => {
    return (searchResults?.papers?.length || 0) > 0
  }, [searchResults])

  const removeFromHistory = useCallback((searchId: string) => {
    searchHistoryManager.removeSearch(searchId)
    setSearchHistory(searchHistoryManager.getHistory())
  }, [])

  const clearSearchHistory = useCallback(() => {
    searchHistoryManager.clearHistory()
    setSearchHistory([])
  }, [])

  // User preferences management
  const updatePreferredSources = useCallback((sources: PaperSource[]) => {
    preferencesManager.setPreferredSources(sources)
    setPreferredSources(sources)
    // Update form default values
    form.setValue('sources', sources)
  }, [form])

  // Load search history and preferences on mount (client-side only)
  useEffect(() => {
    // Mark as hydrated and load client-side data
    setIsHydrated(true)

    // Load search history
    setSearchHistory(searchHistoryManager.getHistory())

    // Load user preferences
    const preferences = preferencesManager.getPreferences()
    setPreferredSources(preferences.preferredSources)

    // Update form with loaded preferences
    form.setValue('sources', preferences.preferredSources)
    form.setValue('maxResults', preferences.maxResults)
  }, [form])

  // Load available sources on mount
  useEffect(() => {
    loadAvailableSources()
  }, [loadAvailableSources])

  return {
    form,
    isSearching,
    searchResults,
    searchError,
    performSearch,
    clearResults,
    availableSources,
    isLoadingSources,
    searchHistory,
    addToHistory,
    removeFromHistory,
    clearSearchHistory,
    preferredSources,
    updatePreferredSources,
    getTotalResults,
    getSearchTime,
    hasResults,
  }
}
