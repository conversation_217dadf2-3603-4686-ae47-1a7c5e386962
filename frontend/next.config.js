/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
    NEXT_PUBLIC_WS_URL: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000',
  },

  // Ultra-fast performance optimizations
  experimental: {
    // optimizeCss: true, // Disabled - requires critters dependency
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },

  // Turbopack configuration (now stable)
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  // Aggressive compression and optimization
  compress: true,
  poweredByHeader: false,

  // Bundle optimization
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // Production optimizations
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            enforce: true,
          },
        },
      }
    }
    return config
  },

  async rewrites() {
    return [
      // Handle debugging/dev tool requests that cause 404s
      {
        source: '/current-url',
        destination: '/api/debug/current-url',
      },
      {
        source: '/.identity',
        destination: '/api/debug/identity',
      },
      // API proxy
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/:path*`,
      },
    ];
  },

  // Ultra-fast dev server configuration
  onDemandEntries: {
    maxInactiveAge: 60 * 1000, // Increased for better caching
    pagesBufferLength: 5,
  },

  // Configure logging
  logging: {
    fetches: {
      fullUrl: false,
    },
  },
};

module.exports = nextConfig; 