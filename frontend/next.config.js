/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
    NEXT_PUBLIC_WS_URL: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000',
  },
  async rewrites() {
    return [
      // Handle debugging/dev tool requests that cause 404s
      {
        source: '/current-url',
        destination: '/api/debug/current-url',
      },
      {
        source: '/.identity',
        destination: '/api/debug/identity',
      },
      // API proxy
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/:path*`,
      },
    ];
  },
  // Suppress dev server logs for debugging endpoints
  onDemandEntries: {
    maxInactiveAge: 25 * 1000,
    pagesBufferLength: 2,
  },
  // Configure logging
  logging: {
    fetches: {
      fullUrl: false,
    },
  },
};

module.exports = nextConfig; 