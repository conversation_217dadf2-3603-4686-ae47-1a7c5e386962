import type { Metadata } from "next"
import { Inter } from "next/font/google"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Paper Downloader",
  description: "Search and download research papers from multiple sources",
  keywords: ["research papers", "academic", "download", "arxiv", "crossref", "google scholar"],
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <div className="min-h-screen bg-background">
          <header className="border-b">
            <div className="container mx-auto px-4 py-4">
              <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold text-primary">
                  Paper Downloader
                </h1>
                <nav className="flex items-center space-x-4">
                  <a 
                    href="/search" 
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Search
                  </a>
                  <a 
                    href="/downloads" 
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Downloads
                  </a>
                </nav>
              </div>
            </div>
          </header>
          <main className="container mx-auto px-4 py-8">
            {children}
          </main>
          <footer className="border-t mt-auto">
            <div className="container mx-auto px-4 py-6">
              <p className="text-center text-muted-foreground text-sm">
                © 2024 Paper Downloader. Built with Next.js and Django.
              </p>
            </div>
          </footer>
        </div>
      </body>
    </html>
  )
} 