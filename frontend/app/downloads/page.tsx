'use client'

import { useEffect, useState } from 'react'
import { 
  Download, 
  CheckCircle, 
  XCircle, 
  Clock, 
  RefreshCw,
  FileText,
  
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { useDownloads } from '@/lib/hooks/useDownloads'
import { DownloadTaskDTO, DownloadStatus } from '@/lib/types'
import { formatDate } from '@/lib/utils'

export default function DownloadsPage() {
  const {
    downloadTasks,
    currentTask,
    currentProgress,
    monitorTask,
    stopMonitoring,
    refreshTaskStatus,
    refreshAllDownloads,
    downloadFile,
    getCompletedDownloads,
    getActiveDownloads,
  } = useDownloads()

  const [activeTab, setActiveTab] = useState<'all' | 'active' | 'completed'>('all')

  // Refresh downloads when the page loads
  useEffect(() => {
    refreshAllDownloads()
  }, [refreshAllDownloads])

  const getStatusIcon = (status: DownloadStatus) => {
    switch (status) {
      case DownloadStatus.COMPLETED:
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case DownloadStatus.FAILED:
        return <XCircle className="w-5 h-5 text-red-500" />
      case DownloadStatus.IN_PROGRESS:
        return <div className="spinner w-5 h-5" />
      default:
        return <Clock className="w-5 h-5 text-yellow-500" />
    }
  }

  const getStatusText = (status: DownloadStatus) => {
    switch (status) {
      case DownloadStatus.COMPLETED:
        return 'Completed'
      case DownloadStatus.FAILED:
        return 'Failed'
      case DownloadStatus.IN_PROGRESS:
        return 'In Progress'
      default:
        return 'Pending'
    }
  }

  const TaskCard = ({ task }: { task: DownloadTaskDTO }) => {
    const isMonitoring = currentTask?.id === task.id
    const progress = currentProgress?.task_id === task.id ? currentProgress.progress : task.progress

    return (
      <Card className="transition-all duration-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getStatusIcon(task.status)}
              <div>
                <CardTitle className="text-lg">
                  Download Task #{task.id.slice(-8)}
                </CardTitle>
                <CardDescription>
                  {task.total_papers} papers • {getStatusText(task.status)}
                </CardDescription>
              </div>
            </div>
            <div className="flex gap-2">
              {!isMonitoring && (task.status === DownloadStatus.IN_PROGRESS || task.status === DownloadStatus.PENDING) && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => monitorTask(task.id)}
                >
                  Monitor
                </Button>
              )}
              {isMonitoring && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={stopMonitoring}
                >
                  Stop Monitoring
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => refreshTaskStatus(task.id)}
              >
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Progress</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>

            {/* Task Details */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-muted-foreground">Total Papers</div>
                <div className="font-medium">{task.total_papers}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Completed</div>
                <div className="font-medium text-green-600">{task.completed_papers}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Failed</div>
                <div className="font-medium text-red-600">{task.failed_papers}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Created</div>
                <div className="font-medium">{formatDate(task.created_at)}</div>
              </div>
            </div>

            {/* Current Progress Message */}
            {isMonitoring && currentProgress && currentProgress.message && (
              <div className="bg-muted/50 p-3 rounded-lg">
                <p className="text-sm">
                  <strong>Status:</strong> {currentProgress.message}
                </p>
                {currentProgress.current_paper && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Current: {currentProgress.current_paper.title}
                  </p>
                )}
              </div>
            )}

            {/* Error Message */}
            {task.error_message && (
              <div className="bg-destructive/10 border border-destructive/20 p-3 rounded-lg">
                <p className="text-sm text-destructive">
                  <strong>Error:</strong> {task.error_message}
                </p>
              </div>
            )}

            {/* Download Links */}
            {task.status === DownloadStatus.COMPLETED && task.download_urls.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Downloaded Files:</h4>
                <div className="grid gap-2">
                  {task.download_urls.map((url, index) => {
                    const filename = url.split('/').pop() || `file_${index + 1}.pdf`
                    return (
                      <div key={index} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                        <div className="flex items-center gap-2">
                          <FileText className="w-4 h-4" />
                          <span className="text-sm">{filename}</span>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => downloadFile(url, filename)}
                        >
                          <Download className="w-4 h-4 mr-1" />
                          Download
                        </Button>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  const filteredTasks = () => {
    switch (activeTab) {
      case 'active':
        return getActiveDownloads()
      case 'completed':
        return getCompletedDownloads()
      default:
        return downloadTasks
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">Download Manager</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Track your paper download tasks with real-time progress updates. 
          Monitor active downloads and access completed files.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Tasks</p>
                <p className="text-2xl font-bold">{downloadTasks.length}</p>
              </div>
              <Download className="w-8 h-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Downloads</p>
                <p className="text-2xl font-bold">{getActiveDownloads().length}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold">{getCompletedDownloads().length}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
        {[
          { key: 'all', label: 'All Tasks' },
          { key: 'active', label: 'Active' },
          { key: 'completed', label: 'Completed' },
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as 'all' | 'active' | 'completed')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab.key
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tasks List */}
      <div className="space-y-4">
        {filteredTasks().length > 0 ? (
          filteredTasks().map((task) => (
            <TaskCard key={task.id} task={task} />
          ))
        ) : (
          <Card>
            <CardContent className="p-8 text-center">
              <Download className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Download Tasks</h3>
              <p className="text-muted-foreground mb-4">
                {activeTab === 'all' 
                  ? "You haven't started any download tasks yet."
                  : `No ${activeTab} download tasks found.`
                }
              </p>
              <Button asChild>
                <a href="/search">Start Searching Papers</a>
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
