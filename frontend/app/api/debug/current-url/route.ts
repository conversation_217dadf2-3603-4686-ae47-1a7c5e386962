import { NextResponse } from 'next/server';

/**
 * Debug endpoint for handling browser dev tools or extension requests
 * This prevents 404 errors in development logs
 */
export async function GET() {
  return NextResponse.json({ status: 'ok', endpoint: 'current-url' }, { status: 200 });
}

export async function POST() {
  return NextResponse.json({ status: 'ok', endpoint: 'current-url' }, { status: 200 });
}

export async function PUT() {
  return NextResponse.json({ status: 'ok', endpoint: 'current-url' }, { status: 200 });
}

export async function DELETE() {
  return NextResponse.json({ status: 'ok', endpoint: 'current-url' }, { status: 200 });
}
