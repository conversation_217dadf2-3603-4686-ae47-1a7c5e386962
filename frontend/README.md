# Paper Downloader Frontend

A modern Next.js frontend for searching and downloading research papers from multiple academic sources.

## Features

- 🔍 **Multi-Source Search**: Search across arXiv, Crossref, Google Scholar, IEEE, PubMed, and Sci-Hub
- 📚 **Bulk Downloads**: Select multiple papers and download them in batch
- 📊 **Real-time Progress**: WebSocket-powered progress tracking for downloads
- 🎨 **Modern UI**: Built with shadcn/ui components and Tailwind CSS
- 🏗️ **MVVM Architecture**: Clean separation of concerns with proper state management
- ⚡ **Performance**: Optimized with Next.js 15 and React 18

## Tech Stack

- **Framework**: Next.js 15.0.3
- **Language**: TypeScript
- **Styling**: Tailwind CSS + shadcn/ui
- **State Management**: Zustand + Custom Hooks
- **Forms**: React Hook Form + Zod validation
- **HTTP Client**: Axios
- **WebSockets**: Socket.io-client
- **Icons**: Lucide React

## Quick Start

### Prerequisites

- Node.js 18.0.0 or higher
- npm or yarn
- Running Django backend (see ../backend/README.md)

### Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Set up environment variables**:
   ```bash
   cp env.example .env.local
   ```
   
   Edit `.env.local` with your configuration:
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:8000/api
   NEXT_PUBLIC_WS_URL=ws://localhost:8000
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## Project Structure

```
frontend/
├── app/                    # Next.js app directory
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   ├── page.tsx          # Home page
│   ├── search/           # Search page
│   └── downloads/        # Downloads page
├── components/
│   ├── ui/               # shadcn/ui components
│   ├── layout/           # Layout components
│   └── features/         # Feature-specific components
├── lib/
│   ├── types.ts          # TypeScript types (Models)
│   ├── utils.ts          # Utility functions
│   ├── hooks/            # Custom hooks (ViewModels)
│   └── services/         # API services
├── public/               # Static assets
└── tailwind.config.js    # Tailwind configuration
```

## Architecture

This frontend follows **MVVM (Model-View-ViewModel)** architecture:

### Models (`lib/types.ts`)
- TypeScript interfaces and types
- Data Transfer Objects (DTOs)
- Enums for status and source types

### Views (`app/` and `components/`)
- React components for UI
- Page components
- Reusable UI components

### ViewModels (`lib/hooks/`)
- Custom React hooks for state management
- Business logic and data fetching
- Real-time updates handling

### Services (`lib/services/`)
- API communication layer
- WebSocket management
- External service integrations

## Key Features

### Search Functionality
- Multi-source academic paper search
- Advanced filtering options
- Real-time search results
- Paper selection for bulk download

### Download Management
- Bulk download task creation
- Real-time progress tracking via WebSocket
- Download history and status
- File management and access

### User Interface
- Responsive design for all devices
- Dark/light theme support (via next-themes)
- Accessible components (WCAG compliant)
- Smooth animations and transitions

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NEXT_PUBLIC_API_URL` | Django backend API URL | `http://localhost:8000/api` |
| `NEXT_PUBLIC_WS_URL` | WebSocket server URL | `ws://localhost:8000` |
| `NEXT_PUBLIC_APP_NAME` | Application name | `Paper Downloader` |
| `NEXT_PUBLIC_APP_VERSION` | Application version | `1.0.0` |

### shadcn/ui Configuration

The project uses shadcn/ui with the "New York" style. Configuration is in `components.json`:

```json
{
  "style": "new-york",
  "rsc": false,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.js",
    "css": "app/globals.css"
  }
}
```

## Development

### Adding New Components

1. **UI Components**: Use shadcn/ui CLI
   ```bash
   npx shadcn@latest add button
   ```

2. **Feature Components**: Create in `components/features/`

3. **Custom Hooks**: Add to `lib/hooks/` following MVVM pattern

### State Management

The app uses a combination of:
- **Zustand**: For global state (if needed)
- **Custom Hooks**: For feature-specific state
- **React Hook Form**: For form state
- **WebSocket**: For real-time updates

### API Integration

All API calls go through the `ApiService` class in `lib/services/api.ts`:

```typescript
import { apiService } from '@/lib/services/api'

// Search papers
const results = await apiService.searchPapers(searchRequest)

// Create download task
const task = await apiService.createDownloadTask(paperIds)
```

## Troubleshooting

### Common Issues

1. **Module not found errors**: Run `npm install`
2. **TypeScript errors**: Run `npm run type-check`
3. **API connection failed**: Check backend is running and environment variables
4. **WebSocket not connecting**: Verify WebSocket URL and backend WebSocket server

### Performance Optimization

- Use React.memo for expensive components
- Implement virtual scrolling for large lists
- Optimize images with Next.js Image component
- Use dynamic imports for code splitting

## Contributing

1. Follow the MVVM architecture pattern
2. Use TypeScript for all new code
3. Follow the existing naming conventions
4. Add proper error handling
5. Write clean, documented code

## License

This project is part of the Paper Downloader application. See the main project LICENSE for details. 