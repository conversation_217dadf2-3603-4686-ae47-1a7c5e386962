#!/usr/bin/env python3
"""
Quick test of text cleaning functionality with real ASM examples.
"""

import sys
import os

# Add backend to path
sys.path.append('backend')

try:
    from apps.papers.data.text_cleaning import clean_title, clean_abstract, clean_author_name, clean_journal_name
    
    print('🧹 Text Cleaning Test Results')
    print('=' * 50)
    
    # Test title cleaning
    title_with_jats = '<jats:title>Comparative Genomics Reveals a Remarkable Biosynthetic Potential of the <jats:italic>Streptomyces</jats:italic> Phylogenetic Lineage Associated with Rugose-Ornamented Spores</jats:title>'
    
    print('\n📝 Title Cleaning:')
    print('BEFORE:', title_with_jats)
    cleaned_title = clean_title(title_with_jats)
    print('AFTER: ', cleaned_title)
    
    # Test abstract cleaning
    abstract_with_jats = '<jats:p> It is now well recognized that members of the genus <jats:italic>Streptomyces</jats:italic> still harbor a large number of cryptic BGCs in their genomes, which are mostly silent under laboratory culture conditions. Activation of transcriptionally silent BGCs is technically challenging and thus forms a bottleneck when taking a gene-first approach for the discovery of new natural products. </jats:p>'
    
    print('\n📄 Abstract Cleaning:')
    print('BEFORE:', abstract_with_jats)
    cleaned_abstract = clean_abstract(abstract_with_jats)
    print('AFTER: ', cleaned_abstract)
    
    # Test author cleaning
    author_with_artifacts = 'Yoon-Hee Chung1*, Hiyoung Kim2†, Chang-Hun Ji3‡ +5 more'
    
    print('\n👤 Author Cleaning:')
    print('BEFORE:', author_with_artifacts)
    cleaned_author = clean_author_name(author_with_artifacts)
    print('AFTER: ', cleaned_author)
    
    # Test complex abstract with multiple sections
    complex_abstract = '<jats:title>ABSTRACT</jats:title> <jats:sec> <jats:title/> <jats:p> Bacteria infecting the plant phloem represent a growing threat worldwide. While these organisms often resist <jats:italic toggle="yes">in vitro</jats:italic> culture, they multiply both in plant sieve elements and hemipteran vectors. Such cross-kingdom parasitic lifestyle has emerged in diverse taxa via distinct ecological routes. In the genus <jats:italic toggle="yes">Arsenophonus</jats:italic> , the phloem pathogens " <jats:italic toggle="yes">Candidatus</jats:italic> Arsenophonus phytopathogenicus" (Ap) and " <jats:italic toggle="yes">Ca</jats:italic> . Phlomobacter fragariae" (Pf) have evolved from insect endosymbionts, but the genetic mechanisms underlying this transition have not been explored. </jats:p> </jats:sec> <jats:sec> <jats:title>IMPORTANCE</jats:title> <jats:p> We investigate the genetic mechanisms of a transition in bacterial lifestyle. We focus on two phloem pathogens belonging to the genus <jats:italic toggle="yes">Arsenophonus</jats:italic> : " <jats:italic toggle="yes">Candidatus</jats:italic> Arsenophonus phytopathogenicus" and " <jats:italic toggle="yes">Ca</jats:italic> . Phlomobacter fragariae." Both bacteria cause economically significant pathologies, and they have likely emerged among facultative insect endosymbionts. </jats:p> </jats:sec>'
    
    print('\n📋 Complex Abstract Cleaning:')
    print('BEFORE:', complex_abstract[:100] + '...')
    cleaned_complex = clean_abstract(complex_abstract)
    print('AFTER: ', cleaned_complex)
    
    # Test journal names
    journal_examples = [
        "mSystems",
        "<em>Applied and Environmental Microbiology</em>",
        "Journal of Bacteriology. 2021; 6(4): e00489-21"
    ]
    
    print('\n📰 Journal Name Cleaning:')
    for journal in journal_examples:
        print(f'BEFORE: {journal}')
        cleaned_journal = clean_journal_name(journal)
        print(f'AFTER:  {cleaned_journal}')
        print()
    
    print('✅ Text cleaning is working properly!')
    print('\nThe cleaning utility successfully handles:')
    print('• JATS XML tags (jats:title, jats:italic, jats:p, etc.)')
    print('• HTML tags (em, strong, etc.)')
    print('• Author name artifacts (superscript numbers, symbols)')
    print('• Complex nested structures')
    print('• Multiple content sections')
    
except Exception as e:
    print(f'❌ Error: {e}')
    import traceback
    traceback.print_exc() 