"""
Test script for the text cleaning functionality.
Tests the cleaning of real ASM search results with JATS XML tags.
"""

import sys
import os

# Add the backend path to import the text cleaning module
sys.path.append('backend')

from backend.apps.papers.data.text_cleaning import (
    clean_title, clean_abstract, clean_author_name, clean_journal_name,
    TextCleaner, CleaningConfig, CleaningLevel, PublicationFormat
)

def test_real_asm_examples():
    """Test with real ASM search result examples from the user."""
    
    print("🧪 Testing Text Cleaning with Real ASM Examples")
    print("=" * 60)
    
    # Example 1: Title with JATS tags
    title_with_jats = """<jats:title>Comparative Genomics Reveals a Remarkable Biosynthetic Potential of the <jats:italic>Streptomyces</jats:italic> Phylogenetic Lineage Associated with Rugose-Ornamented Spores</jats:title>"""
    
    print("\n📝 Testing Title Cleaning:")
    print(f"Raw: {title_with_jats}")
    cleaned_title = clean_title(title_with_jats)
    print(f"Cleaned: {cleaned_title}")
    
    # Example 2: Abstract with complex JATS structure
    abstract_with_jats = """<jats:p> It is now well recognized that members of the genus <jats:italic>Streptomyces</jats:italic> still harbor a large number of cryptic BGCs in their genomes, which are mostly silent under laboratory culture conditions. Activation of transcriptionally silent BGCs is technically challenging and thus forms a bottleneck when taking a gene-first approach for the discovery of new natural products. </jats:p>"""
    
    print("\n📄 Testing Abstract Cleaning:")
    print(f"Raw: {abstract_with_jats}")
    cleaned_abstract = clean_abstract(abstract_with_jats)
    print(f"Cleaned: {cleaned_abstract}")
    
    # Example 3: Complex abstract with multiple sections
    complex_abstract = """<jats:title>ABSTRACT</jats:title> <jats:sec> <jats:title/> <jats:p> Bacteria infecting the plant phloem represent a growing threat worldwide. While these organisms often resist <jats:italic toggle="yes">in vitro</jats:italic> culture, they multiply both in plant sieve elements and hemipteran vectors. Such cross-kingdom parasitic lifestyle has emerged in diverse taxa via distinct ecological routes. In the genus <jats:italic toggle="yes">Arsenophonus</jats:italic> , the phloem pathogens " <jats:italic toggle="yes">Candidatus</jats:italic> Arsenophonus phytopathogenicus" (Ap) and " <jats:italic toggle="yes">Ca</jats:italic> . Phlomobacter fragariae" (Pf) have evolved from insect endosymbionts, but the genetic mechanisms underlying this transition have not been explored. </jats:p> </jats:sec> <jats:sec> <jats:title>IMPORTANCE</jats:title> <jats:p> We investigate the genetic mechanisms of a transition in bacterial lifestyle. We focus on two phloem pathogens belonging to the genus <jats:italic toggle="yes">Arsenophonus</jats:italic> : " <jats:italic toggle="yes">Candidatus</jats:italic> Arsenophonus phytopathogenicus" and " <jats:italic toggle="yes">Ca</jats:italic> . Phlomobacter fragariae." Both bacteria cause economically significant pathologies, and they have likely emerged among facultative insect endosymbionts. </jats:p> </jats:sec>"""
    
    print("\n📋 Testing Complex Abstract Cleaning:")
    print(f"Raw: {complex_abstract[:200]}...")
    cleaned_complex = clean_abstract(complex_abstract)
    print(f"Cleaned: {cleaned_complex}")
    
    # Example 4: Author names with artifacts
    author_with_artifacts = "Yoon-Hee Chung1*, Hiyoung Kim2†, Chang-Hun Ji3‡ +5 more"
    
    print("\n👤 Testing Author Name Cleaning:")
    print(f"Raw: {author_with_artifacts}")
    cleaned_author = clean_author_name(author_with_artifacts)
    print(f"Cleaned: {cleaned_author}")
    
    # Example 5: Journal names
    journal_examples = [
        "mSystems",
        "<em>Applied and Environmental Microbiology</em>",
        "Journal of Bacteriology. 2021; 6(4): e00489-21"
    ]
    
    print("\n📰 Testing Journal Name Cleaning:")
    for journal in journal_examples:
        print(f"Raw: {journal}")
        cleaned_journal = clean_journal_name(journal)
        print(f"Cleaned: {cleaned_journal}")
        print()


def test_cleaning_levels():
    """Test different cleaning levels."""
    
    print("\n🎚️ Testing Different Cleaning Levels")
    print("=" * 40)
    
    test_text = """<jats:p>This is a test with <jats:italic>italic text</jats:italic> and <jats:sup>superscript</jats:sup> and <jats:xref>cross-reference</jats:xref>.</jats:p>"""
    
    cleaner = TextCleaner()
    
    print(f"Original: {test_text}")
    print()
    
    # Test BASIC level
    basic_config = CleaningConfig(level=CleaningLevel.BASIC)
    basic_result = cleaner.clean_text(test_text, basic_config)
    print(f"BASIC: {basic_result}")
    
    # Test STANDARD level
    standard_config = CleaningConfig(level=CleaningLevel.STANDARD)
    standard_result = cleaner.clean_text(test_text, standard_config)
    print(f"STANDARD: {standard_result}")
    
    # Test AGGRESSIVE level
    aggressive_config = CleaningConfig(level=CleaningLevel.AGGRESSIVE)
    aggressive_result = cleaner.clean_text(test_text, aggressive_config)
    print(f"AGGRESSIVE: {aggressive_result}")


def test_format_detection():
    """Test automatic format detection."""
    
    print("\n🔍 Testing Format Detection")
    print("=" * 30)
    
    cleaner = TextCleaner()
    
    test_cases = [
        ("JATS XML", "<jats:p>This is JATS XML content</jats:p>"),
        ("HTML", "<p>This is <em>HTML</em> content</p>"),
        ("Plain Text", "This is plain text content"),
    ]
    
    for name, text in test_cases:
        detected_format = cleaner._detect_format(text)
        print(f"{name}: {detected_format.value}")


def test_edge_cases():
    """Test edge cases and error handling."""
    
    print("\n⚠️ Testing Edge Cases")
    print("=" * 25)
    
    edge_cases = [
        ("Empty string", ""),
        ("None value", None),
        ("Only tags", "<jats:p></jats:p>"),
        ("Nested tags", "<jats:p><jats:italic><jats:sup>nested</jats:sup></jats:italic></jats:p>"),
        ("Malformed tags", "<jats:p>unclosed tag"),
        ("Unicode content", "<jats:p>Café résumé naïve</jats:p>"),
        ("HTML entities", "&lt;test&gt; &amp; &quot;quotes&quot;"),
    ]
    
    for name, test_input in edge_cases:
        print(f"\nTesting {name}: {test_input}")
        try:
            if test_input is not None:
                result = clean_title(str(test_input))
                print(f"Result: {result}")
            else:
                result = clean_title(test_input)
                print(f"Result: {result}")
        except Exception as e:
            print(f"Error: {e}")


def demonstrate_before_after():
    """Show dramatic before/after examples."""
    
    print("\n✨ Before/After Demonstration")
    print("=" * 35)
    
    examples = [
        ("Title", """<jats:title>Comparative Genomics Reveals a Remarkable Biosynthetic Potential of the <jats:italic>Streptomyces</jats:italic> Phylogenetic Lineage</jats:title>"""),
        ("Abstract", """<jats:p>The disaccharide trehalose is accumulated as a storage product by spores of <jats:italic>Streptomyces griseus</jats:italic>. Nongerminating spores used their trehalose reserves slowly when incubated in buffer for several months.</jats:p>"""),
        ("Author", "Yoon-Hee Chung1*, Hiyoung Kim2†"),
    ]
    
    for field_type, raw_text in examples:
        print(f"\n{field_type}:")
        print("─" * 60)
        print(f"BEFORE: {raw_text}")
        
        if field_type == "Title":
            cleaned = clean_title(raw_text)
        elif field_type == "Abstract":
            cleaned = clean_abstract(raw_text)
        elif field_type == "Author":
            cleaned = clean_author_name(raw_text)
        
        print(f"AFTER:  {cleaned}")


if __name__ == "__main__":
    print("🧹 Text Cleaning Test Suite")
    print("=" * 80)
    
    try:
        test_real_asm_examples()
        test_cleaning_levels()
        test_format_detection()
        test_edge_cases()
        demonstrate_before_after()
        
        print("\n✅ All tests completed successfully!")
        print("\nThe text cleaning utility is working properly and can handle:")
        print("• JATS XML tags from ASM publications")
        print("• HTML tags from various sources")
        print("• Author name artifacts (superscript numbers, symbols)")
        print("• Complex nested tag structures")
        print("• Edge cases and malformed content")
        print("• Unicode and HTML entities")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc() 