#!/usr/bin/env python3
import asyncio
import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Import Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

# Setup Django
import django
django.setup()

from backend.apps.papers.data.services import ASMSearchService
from backend.apps.papers.data.dtos import SearchRequestDTO

async def test_asm_search():
    query = 'Comparative Genomics Reveals a Remarkable Biosynthetic Potential of the Streptomyces Phylogenetic Lineage Associated with Rugose-Ornamented Spores'
    
    print(f'Testing ASM search for: {query}')
    print('=' * 80)
    
    service = ASMSearchService()
    request = SearchRequestDTO(query=query, max_results=10)
    
    try:
        papers = await service.search(request)
        print(f'Found {len(papers)} papers')
        
        if papers:
            for i, paper in enumerate(papers, 1):
                print(f'\n--- Paper {i} ---')
                print(f'Title: {paper.title}')
                print(f'DOI: {paper.doi}')
                print(f'Journal: {paper.journal}')
                print(f'URL: {paper.url}')
                print(f'Authors: <AUTHORS>
                if paper.abstract:
                    print(f'Abstract: {paper.abstract[:200]}...')
        else:
            print('No papers found!')
            
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()
    finally:
        await service.session.close()

if __name__ == '__main__':
    asyncio.run(test_asm_search()) 