# Python
**/__pycache__/
**/*.py[cod]
**/*.pyo
**/*.pyd
**/.Python
**/env/
**/venv/
**/ENV/
**/env.bak/
**/venv.bak/

# Django/Migrations
backend/apps/**/migrations/
!backend/apps/**/migrations/__init__.py

# VSCode
.vscode/

# macOS
.DS_Store

# Node
node_modules/
frontend/node_modules/
frontend/.next/
frontend/app/node_modules/
frontend/app/.next/
frontend/app/.nuxt/
frontend/app/.output/
frontend/app/.turbo/
frontend/app/.vercel/
frontend/app/.cache/
frontend/app/.expo/
frontend/app/.expo-shared/

# Build output
frontend/app/build/
frontend/app/dist/
frontend/app/.parcel-cache/
frontend/app/.svelte-kit/
frontend/app/.angular/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Env files
.env
.env.*
frontend/app/.env
frontend/app/.env.*

# Coverage
coverage/
frontend/app/coverage/

# Misc
*.swp
*.swo
*.tmp
*.bak
*.orig
.idea/ 