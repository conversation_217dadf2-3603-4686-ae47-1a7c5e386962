#!/usr/bin/env python3
import os
import sys
import django

# Add backend to path and setup Django
sys.path.insert(0, '/Users/<USER>/Downloads/Codes/paper-downloader/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'paper_downloader.settings')
django.setup()

import asyncio
from apps.papers.data.services import ASMSearchService
from apps.papers.models import SearchRequestDTO

async def quick_test():
    """Quick test of ASM search functionality."""
    print("Testing ASM Search Service...")
    
    service = ASMSearchService()
    async with service:
        # Test with the target paper title
        print("\n🔍 Searching for target Streptomyces paper...")
        request = SearchRequestDTO(
            query='Comparative Genomics Reveals a Remarkable Biosynthetic Potential of the Streptomyces Phylogenetic Lineage Associated with Rugose-Ornamented Spores',
            max_results=5
        )
        papers = await service.search(request)
        print(f'Found {len(papers)} papers')
        
        target_found = False
        for paper in papers:
            print(f'📄 {paper.title[:80]}...')
            print(f'   DOI: {paper.doi}')
            print(f'   Journal: {paper.journal}')
            print(f'   Source: {paper.source}')
            if paper.doi == "10.1128/msystems.00489-21":
                target_found = True
                print("   🎯 TARGET PAPER FOUND!")
            print()
        
        print(f"Target paper found: {'✅' if target_found else '❌'}")

if __name__ == "__main__":
    asyncio.run(quick_test()) 