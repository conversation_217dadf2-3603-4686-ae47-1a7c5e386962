#!/usr/bin/env python3
"""
Test script for the fixed ASM search functionality.
This script tests the improved ASM search to ensure it can find the target paper:
"Comparative Genomics Reveals a Remarkable Biosynthetic Potential of the Streptomyces Phylogenetic Lineage Associated with Rugose-Ornamented Spores"
"""

import asyncio
import sys
import os

# Add the backend directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'paper_downloader.settings')

import django
django.setup()

from apps.papers.data.services import ASMSearchService
from apps.papers.models import SearchRequestDTO


async def test_asm_search():
    """Test the ASM search functionality with the target paper."""
    
    print("=" * 80)
    print("Testing Fixed ASM Search Functionality")
    print("=" * 80)
    
    # Create search service
    service = ASMSearchService()
    
    async with service:
        # Test 1: Search for the specific target paper
        print("\n🔍 Test 1: Searching for the target Streptomyces paper...")
        request1 = SearchRequestDTO(
            query="Comparative Genomics Reveals a Remarkable Biosynthetic Potential of the Streptomyces Phylogenetic Lineage Associated with Rugose-Ornamented Spores",
            max_results=5
        )
        
        papers1 = await service.search(request1)
        print(f"Found {len(papers1)} papers for full title search")
        
        for i, paper in enumerate(papers1, 1):
            print(f"\n📄 Paper {i}:")
            print(f"   Title: {paper.title}")
            print(f"   DOI: {paper.doi}")
            print(f"   Journal: {paper.journal}")
            print(f"   Authors: <AUTHORS>
            print(f"   URL: {paper.url}")
        
        # Test 2: Search with simplified keywords  
        print("\n🔍 Test 2: Searching with simplified keywords...")
        request2 = SearchRequestDTO(
            query="Streptomyces biosynthetic potential rugose ornamented spores",
            max_results=10
        )
        
        papers2 = await service.search(request2)
        print(f"Found {len(papers2)} papers for keyword search")
        
        for i, paper in enumerate(papers2, 1):
            print(f"\n📄 Paper {i}:")
            print(f"   Title: {paper.title}")
            print(f"   DOI: {paper.doi}")
            print(f"   Journal: {paper.journal}")
            
        # Test 3: General Streptomyces search
        print("\n🔍 Test 3: General Streptomyces search...")
        request3 = SearchRequestDTO(
            query="Streptomyces",
            max_results=10
        )
        
        papers3 = await service.search(request3)
        print(f"Found {len(papers3)} papers for general Streptomyces search")
        
        # Count ASM papers
        asm_papers = [p for p in papers3 if 'american society for microbiology' in p.journal.lower() or 
                     any(j in p.journal.lower() for j in ['msystems', 'mbio', 'msphere'])]
        print(f"   ASM papers found: {len(asm_papers)}")
        
        for i, paper in enumerate(asm_papers[:5], 1):
            print(f"\n📄 ASM Paper {i}:")
            print(f"   Title: {paper.title}")
            print(f"   DOI: {paper.doi}")
            print(f"   Journal: {paper.journal}")
        
        # Test 4: Microbiology search
        print("\n🔍 Test 4: Microbiology search...")
        request4 = SearchRequestDTO(
            query="microbiology genomics",
            max_results=15
        )
        
        papers4 = await service.search(request4)
        print(f"Found {len(papers4)} papers for microbiology genomics search")
        
        # Check if we found the target paper in any search
        target_found = False
        target_doi = "10.1128/msystems.00489-21"
        
        all_papers = papers1 + papers2 + papers3 + papers4
        for paper in all_papers:
            if paper.doi and target_doi in paper.doi:
                target_found = True
                print(f"\n🎯 TARGET PAPER FOUND!")
                print(f"   Title: {paper.title}")
                print(f"   DOI: {paper.doi}")
                print(f"   Journal: {paper.journal}")
                break
        
        if not target_found:
            print(f"\n❌ Target paper with DOI {target_doi} was not found in any search")
        
        print("\n" + "=" * 80)
        print("ASM Search Test Summary:")
        print(f"  • Full title search: {len(papers1)} papers")
        print(f"  • Keyword search: {len(papers2)} papers")
        print(f"  • General Streptomyces: {len(papers3)} papers")
        print(f"  • Microbiology genomics: {len(papers4)} papers")
        print(f"  • Target paper found: {'✅' if target_found else '❌'}")
        print("=" * 80)


async def test_crossref_directly():
    """Test the Crossref search directly to verify the fix."""
    
    print("\n" + "=" * 80)
    print("Testing Crossref Search Directly")
    print("=" * 80)
    
    import aiohttp
    
    async with aiohttp.ClientSession() as session:
        # Test the exact search that should find the target paper
        url = "https://api.crossref.org/works"
        params = {
            'query': 'Comparative Genomics Reveals a Remarkable Biosynthetic Potential Streptomyces',
            'rows': 10,
            'mailto': '<EMAIL>'
        }
        
        print(f"🔍 Testing direct Crossref search...")
        print(f"   Query: {params['query']}")
        
        async with session.get(url, params=params) as response:
            if response.status == 200:
                data = await response.json()
                items = data.get('message', {}).get('items', [])
                
                print(f"   Found {len(items)} total results")
                
                # Look for ASM papers
                asm_papers = []
                target_found = False
                
                for item in items:
                    publisher = item.get('publisher', '').lower()
                    title = item.get('title', [''])[0] if item.get('title') else ''
                    doi = item.get('DOI', '')
                    
                    if 'american society for microbiology' in publisher:
                        asm_papers.append({
                            'title': title,
                            'doi': doi,
                            'publisher': item.get('publisher', '')
                        })
                        
                        if doi == "10.1128/msystems.00489-21":
                            target_found = True
                            print(f"\n🎯 TARGET PAPER FOUND in Crossref!")
                            print(f"   Title: {title}")
                            print(f"   DOI: {doi}")
                            print(f"   Publisher: {item.get('publisher', '')}")
                
                print(f"\n   ASM papers in results: {len(asm_papers)}")
                for i, paper in enumerate(asm_papers, 1):
                    print(f"   {i}. {paper['title'][:80]}...")
                    print(f"      DOI: {paper['doi']}")
                
                if not target_found:
                    print(f"\n❌ Target paper not found in direct Crossref search")
            else:
                print(f"   ❌ Crossref request failed with status {response.status}")


if __name__ == "__main__":
    asyncio.run(test_asm_search())
    asyncio.run(test_crossref_directly()) 