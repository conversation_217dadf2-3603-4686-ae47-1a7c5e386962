{"name": "paper-downloader", "version": "1.0.0", "description": "Paper Downloader - Full-stack application for searching and downloading research papers", "private": true, "scripts": {"dev": "concurrently --kill-others --prefix-colors \"blue,green\" --prefix \"[{name}]\" --names \"backend,frontend\" \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && bash -c 'source venv/bin/activate && python manage.py runserver'", "dev:frontend": "cd frontend && npm run dev", "start": "npm run dev", "install:all": "cd frontend && npm install", "setup": "npm run install:all && cd backend && bash -c 'source venv/bin/activate && pip install -r requirements.txt && python manage.py migrate'", "clean": "cd frontend && rm -rf node_modules package-lock.json && npm install", "reset:db": "cd backend && rm -f db.sqlite3 && bash -c 'source venv/bin/activate && python manage.py migrate'", "logs:backend": "cd backend && bash -c 'source venv/bin/activate && python manage.py runserver --verbosity=2'", "logs:frontend": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "test": "concurrently --kill-others --prefix-colors \"blue,green\" --prefix \"[{name}]\" --names \"backend,frontend\" \"cd backend && bash -c 'source venv/bin/activate && python manage.py test'\" \"cd frontend && npm run lint\"", "kill:ports": "lsof -ti:3000,8000 | xargs kill -9 || true", "restart": "npm run kill:ports && npm run dev", "logs:clean": "concurrently --kill-others --prefix-colors \"blue,green\" --prefix \"[{name}]\" --names \"backend,frontend\" \"npm run dev:backend\" \"cd frontend && npm run dev -- --quiet\""}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "python": ">=3.13.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/paper-downloader.git"}, "keywords": ["research", "papers", "downloader", "academic", "arxiv", "pubmed", "django", "nextjs", "mvvm"], "author": "Your Name", "license": "MIT"}