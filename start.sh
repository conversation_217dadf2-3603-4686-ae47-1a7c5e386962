#!/bin/bash

# Paper Downloader - Unified Startup Script
# This script starts both backend and frontend services in a single terminal

echo "🚀 Starting Paper Downloader Application..."
echo "📍 Backend will run on: http://localhost:8000"
echo "📍 Frontend will run on: http://localhost:3000"
echo "📍 API Documentation: http://localhost:8000/api/v1/docs"
echo ""

# Check if node_modules exists, if not install dependencies
if [ ! -d "node_modules" ]; then
    echo "📦 Installing root dependencies..."
    npm install
fi

# Check if frontend node_modules exists
if [ ! -d "frontend/node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    cd frontend && npm install && cd ..
fi

# Check if virtual environment exists
if [ ! -d "backend/venv" ]; then
    echo "❌ Virtual environment not found! Please run setup first:"
    echo "   cd backend && python -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
    exit 1
fi

# Check if database exists, if not migrate
if [ ! -f "backend/db.sqlite3" ]; then
    echo "🗃️  Setting up database..."
    cd backend
    source venv/bin/activate
    python manage.py migrate
    cd ..
fi

echo "🔥 Starting both services with unified logging..."
echo "💡 Use Ctrl+C to stop all services"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Start both services using npm script
npm run dev 